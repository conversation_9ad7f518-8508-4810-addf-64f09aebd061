/**
 * Visibility state cleanup actions for the workout store
 * Handles cleanup when app goes to background/foreground
 */

import type { WorkoutState } from './types'
import { logger } from '@/utils/logger'

export const createVisibilityActions = (
  set: (
    partial:
      | Partial<WorkoutState>
      | ((state: WorkoutState) => Partial<WorkoutState>)
  ) => void,
  get: () => WorkoutState
) => {
  // Define the actions object to allow self-referencing
  const actions = {
    /**
     * Clean up all pending operations when app goes to background
     */
    handleAppBackground: () => {
      const { loadingStates, restTimerState, clearAllLoadingStates } = get()

      logger.log('[WorkoutStore] App going to background, cleaning up states')

      // Clear all loading states
      if (loadingStates.size > 0) {
        clearAllLoadingStates()
      }

      // Pause rest timer if active
      if (restTimerState.isActive) {
        set({
          restTimerState: {
            ...restTimerState,
            isActive: false,
            pausedAt: Date.now(),
          },
        })
      }

      // Clear any temporary errors
      set({
        errors: new Map(),
        error: null,
      })
    },

    /**
     * Refresh all prefetched exercise recommendations
     * Called when app returns from background to ensure data is fresh
     */
    refreshAllPrefetchedExercises: async () => {
      const {
        prefetchedExerciseIds,
        loadExerciseRecommendation,
        workoutSession,
        currentWorkout,
      } = get()

      // Only refresh if we have an active workout session
      if (!workoutSession || !currentWorkout) {
        return
      }

      // Only refresh if we have prefetched exercises
      if (!prefetchedExerciseIds || prefetchedExerciseIds.length === 0) {
        logger.log('[WorkoutStore] No prefetched exercises to refresh')
        return
      }

      logger.log(
        `[WorkoutStore] Refreshing ${prefetchedExerciseIds.length} prefetched exercise recommendations`
      )

      // Refresh all prefetched exercises in parallel
      const refreshPromises = prefetchedExerciseIds.map(async (exerciseId) => {
        try {
          await loadExerciseRecommendation(exerciseId)
          logger.log(`[WorkoutStore] Refreshed exercise ${exerciseId}`)
        } catch (error) {
          logger.error(
            `[WorkoutStore] Failed to refresh exercise ${exerciseId}:`,
            error
          )
          // Don't throw - let other exercises continue refreshing
        }
      })

      // Wait for all refreshes to complete (or fail)
      await Promise.allSettled(refreshPromises)
      logger.log(
        '[WorkoutStore] Background refresh of prefetched exercises completed'
      )
    },

    /**
     * Restore and refresh state when app returns from background
     */
    handleAppForeground: async () => {
      const {
        restTimerState,
        workoutSession,
        currentWorkout,
        loadExerciseRecommendation,
        exercises,
        currentExerciseIndex,
      } = get()

      logger.log(
        '[WorkoutStore] App returning from background, restoring states'
      )

      // Resume rest timer if it was paused
      if (restTimerState.pausedAt) {
        const pauseDuration = Date.now() - restTimerState.pausedAt
        const adjustedDuration = Math.max(
          0,
          restTimerState.duration - Math.floor(pauseDuration / 1000)
        )

        set({
          restTimerState: {
            ...restTimerState,
            isActive: true,
            duration: adjustedDuration,
            pausedAt: undefined,
          },
        })
      }

      // If we have an active workout session, refresh exercise recommendations
      if (workoutSession && currentWorkout) {
        // Refresh all prefetched exercises in the background
        actions.refreshAllPrefetchedExercises().catch((error) => {
          logger.error(
            '[WorkoutStore] Background refresh of prefetched exercises failed:',
            error
          )
        })

        // Also refresh current exercise (may or may not be in prefetched list)
        const currentExercise = exercises[currentExerciseIndex]
        if (currentExercise) {
          // Only refresh current exercise if it's not already being refreshed
          // as part of the prefetched exercises
          const { prefetchedExerciseIds } = get()
          const isCurrentExercisePrefetched = prefetchedExerciseIds.includes(
            currentExercise.Id
          )

          if (!isCurrentExercisePrefetched) {
            loadExerciseRecommendation(currentExercise.Id).catch((error) => {
              logger.error(
                '[WorkoutStore] Failed to reload current exercise recommendation:',
                error
              )
            })
          }
        }

        // Check if session is too old (over 12 hours)
        const sessionAge = Date.now() - workoutSession.startTime.getTime()
        const TWELVE_HOURS = 12 * 60 * 60 * 1000

        if (sessionAge > TWELVE_HOURS) {
          logger.warn('[WorkoutStore] Workout session is over 12 hours old')
          // You might want to show a warning to the user here
          set({
            error:
              'Your workout session has been running for over 12 hours. Consider saving and starting a new session.',
          })
        }
      }
    },

    /**
     * Clear all timeout-based operations
     */
    clearAllTimeouts: () => {
      // This would be used if we had any setTimeout operations in the store
      // For now, it's a placeholder for future timeout management
      logger.log('[WorkoutStore] Clearing all timeouts')
    },

    /**
     * Reset all transient states that shouldn't persist across visibility changes
     */
    resetTransientStates: () => {
      set({
        isLoading: false,
        loadingStates: new Map(),
        errors: new Map(),
      })
    },
  }

  return actions
}
