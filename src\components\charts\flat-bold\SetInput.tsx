import React from 'react'
import { cn } from '@/lib/utils'

interface SetInputProps {
  weight: number
  reps: number
  onWeightChange: (value: number) => void
  onRepsChange: (value: number) => void
  className?: string
}

export function SetInput({
  weight,
  reps,
  onWeightChange,
  onRepsChange,
  className,
}: SetInputProps) {
  return (
    <div className={cn('grid grid-cols-2 gap-4', className)}>
      <div>
        <label
          htmlFor="weight-input"
          className="block text-black font-bold uppercase text-sm mb-2"
        >
          WEIGHT (LBS)
        </label>
        <input
          id="weight-input"
          type="number"
          value={weight}
          onChange={(e) => onWeightChange(Number(e.target.value))}
          className={cn(
            'w-full h-16 text-3xl font-bold text-center',
            'border-4 border-black bg-white',
            'focus:border-[#00FF88] focus:outline-none',
            'transition-none min-w-[52px]'
          )}
        />
      </div>

      <div>
        <label
          htmlFor="reps-input"
          className="block text-black font-bold uppercase text-sm mb-2"
        >
          REPS
        </label>
        <input
          id="reps-input"
          type="number"
          value={reps}
          onChange={(e) => onRepsChange(Number(e.target.value))}
          className={cn(
            'w-full h-16 text-3xl font-bold text-center',
            'border-4 border-black bg-white',
            'focus:border-[#00FF88] focus:outline-none',
            'transition-none min-w-[52px]'
          )}
        />
      </div>
    </div>
  )
}
