/**
 * Prefetch actions for the workout store
 */

import type { WorkoutState } from './types'

export interface PrefetchActions {
  setPrefetchStatus: (
    status: Record<number, 'loading' | 'success' | 'error'>
  ) => void
  setPrefetchedExerciseIds: (ids: number[]) => void
  isExercisePrefetched: (exerciseId: number) => boolean
  clearExercisePrefetch: (exerciseId: number) => void
  getPrefetchProgress: () => {
    total: number
    completed: number
    failed: number
    inProgress: number
    percentage: number
  }
}

export const createPrefetchActions = (
  set: (
    partial:
      | Partial<WorkoutState>
      | ((state: WorkoutState) => Partial<WorkoutState>)
  ) => void,
  get: () => WorkoutState
): PrefetchActions => ({
  setPrefetchStatus: (status) => {
    set((state) => ({
      prefetchStatus: { ...state.prefetchStatus, ...status },
    }))
  },

  setPrefetchedExerciseIds: (ids) => {
    set({ prefetchedExerciseIds: ids })
  },

  isExercisePrefetched: (exerciseId) => {
    const { prefetchStatus } = get()
    return prefetchStatus[exerciseId] === 'success'
  },

  clearExercisePrefetch: (exerciseId) => {
    set((state) => {
      const newPrefetchStatus = { ...state.prefetchStatus }
      delete newPrefetchStatus[exerciseId]

      const newPrefetchedIds = state.prefetchedExerciseIds.filter(
        (id) => id !== exerciseId
      )

      return {
        prefetchStatus: newPrefetchStatus,
        prefetchedExerciseIds: newPrefetchedIds,
      }
    })
  },

  getPrefetchProgress: () => {
    const { prefetchStatus } = get()
    const entries = Object.entries(prefetchStatus)
    const total = entries.length

    if (total === 0) {
      return {
        total: 0,
        completed: 0,
        failed: 0,
        inProgress: 0,
        percentage: 0,
      }
    }

    const completed = entries.filter(
      ([, status]) => status === 'success'
    ).length
    const failed = entries.filter(([, status]) => status === 'error').length
    const inProgress = entries.filter(
      ([, status]) => status === 'loading'
    ).length

    return {
      total,
      completed,
      failed,
      inProgress,
      percentage: Math.round((completed / total) * 100),
    }
  },
})
