/**
 * Test fixtures for cache system testing
 */

import type { CacheEntry, CacheMetadata } from '../types'
import { CACHE_ENTRY_VERSION } from '../types'

/**
 * Sample cache entries of different types
 */
export const sampleEntries = {
  string: {
    key: 'string-key',
    value: 'Hello, World!',
    metadata: {
      size: 26, // Approximate size
      created: 1640995200000, // 2022-01-01 00:00:00 UTC
      accessed: 1640995200000,
      expires: 0,
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    } as CacheMetadata,
  },

  number: {
    key: 'number-key',
    value: 42,
    metadata: {
      size: 4,
      created: 1640995200000,
      accessed: 1640995200000,
      expires: 0,
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    } as CacheMetadata,
  },

  boolean: {
    key: 'boolean-key',
    value: true,
    metadata: {
      size: 8,
      created: 1640995200000,
      accessed: 1640995200000,
      expires: 0,
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    } as CacheMetadata,
  },

  array: {
    key: 'array-key',
    value: [1, 2, 3, 'four', true],
    metadata: {
      size: 50,
      created: 1640995200000,
      accessed: 1640995200000,
      expires: 0,
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    } as CacheMetadata,
  },

  object: {
    key: 'object-key',
    value: {
      id: 123,
      name: 'Test Object',
      active: true,
      metadata: {
        created: '2022-01-01T00:00:00Z',
        tags: ['test', 'fixture'],
      },
    },
    metadata: {
      size: 150,
      created: 1640995200000,
      accessed: 1640995200000,
      expires: 0,
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    } as CacheMetadata,
  },

  null: {
    key: 'null-key',
    value: null,
    metadata: {
      size: 8,
      created: 1640995200000,
      accessed: 1640995200000,
      expires: 0,
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    } as CacheMetadata,
  },
}

/**
 * Edge case values for testing
 */
export const edgeCaseValues = {
  emptyString: '',
  emptyArray: [],
  emptyObject: {},
  
  // Special characters
  specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  
  // Unicode characters
  unicode: '🚀 Unicode test 中文 العربية 🎉',
  
  // Very long string
  longString: 'x'.repeat(10000),
  
  // Large array
  largeArray: Array.from({ length: 1000 }, (_, i) => i),
  
  // Complex nested object
  complexObject: {
    level1: {
      level2: {
        level3: {
          level4: {
            level5: {
              data: 'deeply nested',
              array: [1, 2, 3],
              object: { key: 'value' },
            },
          },
        },
      },
    },
    siblings: ['a', 'b', 'c'],
    metadata: {
      created: new Date().toISOString(),
      version: 1,
    },
  },
  
  // Circular reference (will cause serialization issues)
  createCircular() {
    const obj: any = { name: 'circular' }
    obj.self = obj
    return obj
  },
  
  // Very large object
  largeObject: {
    users: Array.from({ length: 1000 }, (_, i) => ({
      id: i,
      name: `User ${i}`,
      email: `user${i}@example.com`,
      profile: {
        age: 20 + (i % 50),
        city: `City ${i % 100}`,
        interests: [`hobby${i % 10}`, `sport${i % 5}`],
      },
      settings: {
        notifications: i % 2 === 0,
        privacy: i % 3 === 0 ? 'public' : 'private',
        theme: i % 4 === 0 ? 'dark' : 'light',
      },
    })),
  },
}

/**
 * Performance test datasets
 */
export const performanceDatasets = {
  small: {
    name: 'Small Dataset',
    entries: Array.from({ length: 10 }, (_, i) => ({
      key: `small-key-${i}`,
      value: `small-value-${i}`,
      size: 50,
    })),
  },

  medium: {
    name: 'Medium Dataset',
    entries: Array.from({ length: 100 }, (_, i) => ({
      key: `medium-key-${i}`,
      value: {
        id: i,
        data: `medium-data-${i}`,
        timestamp: Date.now() + i,
      },
      size: 100,
    })),
  },

  large: {
    name: 'Large Dataset',
    entries: Array.from({ length: 1000 }, (_, i) => ({
      key: `large-key-${i}`,
      value: {
        id: i,
        data: `large-data-${i}`.repeat(10),
        metadata: {
          created: Date.now() + i,
          tags: [`tag${i % 10}`, `category${i % 5}`],
        },
      },
      size: 500,
    })),
  },

  xlarge: {
    name: 'Extra Large Dataset',
    entries: Array.from({ length: 10000 }, (_, i) => ({
      key: `xlarge-key-${i}`,
      value: `xlarge-value-${i}`,
      size: 50,
    })),
  },
}

/**
 * TTL test scenarios
 */
export const ttlScenarios = {
  // Already expired
  expired: {
    key: 'expired-key',
    value: 'expired-value',
    metadata: {
      size: 50,
      created: Date.now() - 10000, // 10 seconds ago
      accessed: Date.now() - 10000,
      expires: Date.now() - 5000, // Expired 5 seconds ago
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    } as CacheMetadata,
  },

  // Expires soon
  expiringSoon: {
    key: 'expiring-soon-key',
    value: 'expiring-soon-value',
    metadata: {
      size: 50,
      created: Date.now() - 5000,
      accessed: Date.now() - 5000,
      expires: Date.now() + 1000, // Expires in 1 second
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    } as CacheMetadata,
  },

  // Never expires
  neverExpires: {
    key: 'never-expires-key',
    value: 'never-expires-value',
    metadata: {
      size: 50,
      created: Date.now() - 86400000, // 1 day ago
      accessed: Date.now() - 3600000, // 1 hour ago
      expires: 0, // Never expires
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    } as CacheMetadata,
  },

  // Long TTL
  longTTL: {
    key: 'long-ttl-key',
    value: 'long-ttl-value',
    metadata: {
      size: 50,
      created: Date.now(),
      accessed: Date.now(),
      expires: Date.now() + 86400000, // Expires in 1 day
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    } as CacheMetadata,
  },
}

/**
 * Namespace test data
 */
export const namespaceData = {
  auth: [
    { key: 'user-token', value: 'abc123', namespace: 'auth' },
    { key: 'refresh-token', value: 'def456', namespace: 'auth' },
    { key: 'user-id', value: '12345', namespace: 'auth' },
  ],

  cache: [
    { key: 'api-response-1', value: { data: 'response1' }, namespace: 'cache' },
    { key: 'api-response-2', value: { data: 'response2' }, namespace: 'cache' },
  ],

  session: [
    { key: 'session-id', value: 'session123', namespace: 'session' },
    { key: 'last-activity', value: Date.now(), namespace: 'session' },
  ],

  temp: [
    { key: 'temp-data-1', value: 'temporary1', namespace: 'temp' },
    { key: 'temp-data-2', value: 'temporary2', namespace: 'temp' },
    { key: 'temp-data-3', value: 'temporary3', namespace: 'temp' },
  ],
}

/**
 * Error scenarios for testing
 */
export const errorScenarios = {
  invalidKeys: [
    '', // Empty key
    'key:with:colons', // Contains colons
    null, // Null key
    undefined, // Undefined key
    123, // Number key
    {}, // Object key
  ],

  invalidValues: [
    undefined, // Undefined value (should be rejected)
  ],

  serializationErrors: [
    // Circular reference
    (() => {
      const obj: any = { name: 'circular' }
      obj.self = obj
      return obj
    })(),
  ],

  quotaExceeded: {
    // Very large data that might exceed storage quota
    largeData: 'x'.repeat(10 * 1024 * 1024), // 10MB string
  },
}

/**
 * Mock API responses for testing integrations
 */
export const mockApiResponses = {
  userProfile: {
    id: 123,
    name: 'John Doe',
    email: '<EMAIL>',
    preferences: {
      theme: 'dark',
      notifications: true,
    },
  },

  workoutData: {
    id: 456,
    name: 'Push Day',
    exercises: [
      { id: 1, name: 'Bench Press', sets: 3, reps: 10 },
      { id: 2, name: 'Push-ups', sets: 3, reps: 15 },
    ],
    duration: 3600, // 1 hour
  },

  programData: {
    id: 789,
    name: 'Beginner Program',
    weeks: 12,
    workouts: [
      { day: 'Monday', type: 'Push' },
      { day: 'Wednesday', type: 'Pull' },
      { day: 'Friday', type: 'Legs' },
    ],
  },
}

/**
 * Helper function to create a cache entry
 */
export function createCacheEntry<T>(
  value: T,
  overrides: Partial<CacheMetadata> = {}
): CacheEntry<T> {
  return {
    value,
    metadata: {
      size: JSON.stringify(value).length * 2,
      created: Date.now(),
      accessed: Date.now(),
      expires: 0,
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
      ...overrides,
    },
  }
}

/**
 * Helper function to create multiple cache entries
 */
export function createCacheEntries<T>(
  entries: Array<{ key: string; value: T; metadata?: Partial<CacheMetadata> }>
): Map<string, CacheEntry<T>> {
  const result = new Map<string, CacheEntry<T>>()
  
  for (const { key, value, metadata = {} } of entries) {
    result.set(key, createCacheEntry(value, metadata))
  }
  
  return result
}
