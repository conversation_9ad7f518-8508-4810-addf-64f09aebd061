/**
 * Types for the workout store
 */

import type {
  WorkoutTemplateModel,
  WorkoutTemplateGroupModel,
  ExerciseModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'
import type { WorkoutSession } from '@/types/app'
import type { GetUserWorkoutProgramTimeZoneInfoResponse } from '@/services/api/workout'

/**
 * Represents a user's exercise swap preference
 */
export interface ExerciseSwapContext {
  workoutId: number
  sourceExerciseId: number
  sourceBodyPartId: number
  targetExerciseId: number
  bodyPartId: number
  label: string
  isSystemExercise: boolean
  isSwapTarget: boolean
  isFinished: boolean
  swapDate: string // ISO date when swap was made
  // Store original exercise data for better reverting
  originalExercise: {
    label: string
    bodyPartId: number
    isSystemExercise: boolean
    equipmentId?: number
    setStyle?: string
    isBodyweight?: boolean
    isFlexibility?: boolean
  }
}

export interface CurrentSetData {
  reps?: number
  weight?: number
  rir?: number
}

export interface RestTimerState {
  isActive: boolean
  duration: number // in seconds
  pausedAt?: number // timestamp when paused
  nextSetInfo?: {
    reps: number
    weight: number
    unit: 'kg' | 'lbs'
  }
}

export interface CachedAPIData {
  userProgramInfo: GetUserWorkoutProgramTimeZoneInfoResponse | null
  userWorkouts: WorkoutTemplateModel[] | null
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  exerciseRecommendations: Record<number, RecommendationModel | null>
  lastUpdated: {
    userProgramInfo: number
    userWorkouts: number
    todaysWorkout: number
    exerciseRecommendations: Record<number, number>
  }
  // Session timestamp for background resuming validation
  sessionTimestamp?: number
}

// Cache statistics interface
export interface CacheStats {
  hits: number
  misses: number
  hitRate: number
  operationCount: number
  averageLatency: number
  totalLatency: number
  totalSize: number
  itemCount: number
  oldestDataAge: number
  freshDataCount: number
  staleDataCount: number
  hydrationTime: number
}

// Cache health interface
export interface CacheHealth {
  isHealthy: boolean
  warnings: string[]
}

export interface WorkoutState {
  // State
  currentWorkout: WorkoutTemplateModel | null
  currentProgram: WorkoutTemplateGroupModel | null
  exercises: ExerciseModel[]
  currentExerciseIndex: number
  currentSetIndex: number
  workoutSession: WorkoutSession | null
  isLoading: boolean
  error: string | null
  currentSetData: CurrentSetData
  loadingStates: Map<number, boolean>
  errors: Map<number, Error>

  // Exercise Swap State
  exerciseSwaps: Record<number, ExerciseSwapContext>

  // Preview Skip State
  previewExerciseSkips: Set<number>

  // Rest Timer State
  restTimerState: RestTimerState

  // Cache State
  cachedData: CachedAPIData
  hasHydrated: boolean
  cacheVersion: number
  cacheStats: CacheStats

  // Prefetch State
  prefetchStatus: Record<number, 'loading' | 'success' | 'error'>
  prefetchedExerciseIds: number[]

  // Callbacks
  onExerciseStatusUpdate?: (
    exerciseId: number,
    statusData: {
      exerciseId: number
      isInProgress: boolean
      isFinished: boolean
      completedSets: number
      recommendedSets: number
    }
  ) => void

  // Actions
  setWorkout: (workout: WorkoutTemplateModel) => void
  startWorkout: () => void
  nextSet: () => void
  setCurrentSetIndex: (setIndex: number) => void
  nextExercise: () => void
  skipExercise: (exerciseId: number) => void
  skipPreviewExercise: (exerciseId: number) => void
  setCurrentExerciseById: (exerciseId: number) => void
  saveSet: (setData: WorkoutLogSerieModel) => void
  completeWorkout: () => void
  updateCurrentSet: (data: CurrentSetData) => void
  setLoading: (loading: boolean) => void
  setError: (error: string) => void
  resetWorkout: () => void
  clearCache: () => void
  clearLoadingState: (exerciseId: number) => void
  clearAllLoadingStates: () => void
  setRestTimerState: (state: RestTimerState) => void

  // Workout Loading Actions
  loadWorkoutProgram: () => Promise<void>
  loadWorkoutDetails: (workoutId: number) => Promise<void>
  loadExerciseRecommendation: (exerciseId: number) => Promise<void>
  loadAllExerciseRecommendations: () => Promise<void>
  getCacheKey: (userId: string, exerciseId: number, workoutId: number) => string

  // Cache Actions
  setCachedUserProgramInfo: (
    data: GetUserWorkoutProgramTimeZoneInfoResponse | null
  ) => void
  setCachedUserWorkouts: (data: WorkoutTemplateModel[] | null) => void
  setCachedTodaysWorkout: (data: WorkoutTemplateGroupModel[] | null) => void
  setCachedExerciseRecommendation: (
    exerciseId: number,
    data: RecommendationModel | null
  ) => void
  getCachedUserProgramInfo: () => GetUserWorkoutProgramTimeZoneInfoResponse | null
  getCachedUserWorkouts: () => WorkoutTemplateModel[] | null
  getCachedTodaysWorkout: () => WorkoutTemplateGroupModel[] | null
  getCachedExerciseRecommendation: (
    exerciseId: number
  ) => RecommendationModel | null | undefined
  setHasHydrated: (hydrated: boolean) => void
  handleCacheVersionMismatch: () => void
  getCacheSize: () => number
  isCacheStale: (
    type:
      | 'userProgramInfo'
      | 'userWorkouts'
      | 'todaysWorkout'
      | 'exerciseRecommendation',
    exerciseId?: number
  ) => boolean
  clearExpiredCache: () => void

  // Cache Monitoring
  getCacheStats: () => CacheStats
  resetCacheStats: () => void
  logCacheContents: () => void
  clearAllCache: () => void
  getCacheHealth: () => CacheHealth

  // Visibility Actions
  handleAppBackground: () => void
  handleAppForeground: () => Promise<void>
  refreshAllPrefetchedExercises: () => Promise<void>
  clearAllTimeouts: () => void
  resetTransientStates: () => void

  // Getters
  getCurrentExercise: () => ExerciseModel | null
  getCurrentSet: () => CurrentSetData | null
  isWorkoutComplete: () => boolean
  getWorkoutDuration: () => number
  getNextExercise: () => ExerciseModel | null
  getRestDuration: () => number
  getExerciseProgress: () => {
    totalSets: number
    completedSets: number
    isFirstWorkSet: boolean
    currentSetIsWarmup: boolean
    hasRIR: boolean
  } | null
  updateSetRIR: (exerciseId: number) => void

  // Exercise Swap Actions
  swapExercise: (
    workoutId: number,
    sourceExerciseId: number,
    targetExerciseId: number,
    targetExercise: ExerciseModel
  ) => Promise<void>
  revertExerciseSwap: (
    workoutId: number,
    sourceExerciseId: number
  ) => Promise<void>
  getSwapForExercise: (
    workoutId: number,
    exerciseId: number
  ) => ExerciseSwapContext | null
  applyExerciseSwaps: (workout: WorkoutTemplateModel) => WorkoutTemplateModel
  clearExerciseSwaps: () => void

  // Selectors
  getExerciseRecommendation: (
    exerciseId: number
  ) => RecommendationModel | undefined
  isExerciseLoading: (exerciseId: number) => boolean
  getWorkoutExercises: () => ExerciseModel[]

  // Prefetch Actions
  setPrefetchStatus: (
    status: Record<number, 'loading' | 'success' | 'error'>
  ) => void
  setPrefetchedExerciseIds: (ids: number[]) => void
  isExercisePrefetched: (exerciseId: number) => boolean
  clearExercisePrefetch: (exerciseId: number) => void
  getPrefetchProgress: () => {
    total: number
    completed: number
    failed: number
    inProgress: number
    percentage: number
  }
}
