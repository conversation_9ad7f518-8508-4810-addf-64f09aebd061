import React from 'react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useLoginPrefetch } from '../useLoginPrefetch'

import { usePrefetchUserStats } from '../useUserStats'
import { getUserWorkoutProgramInfo } from '@/services/api/workout'
import { workoutApi } from '@/api/workouts'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock the prefetch hook
vi.mock('../useUserStats', () => ({
  usePrefetchUserStats: vi.fn(() => ({
    prefetch: vi.fn(),
  })),
}))

// Mock workout API
vi.mock('@/services/api/workout', () => ({
  getUserWorkoutProgramInfo: vi.fn(),
}))

vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserWorkout: vi.fn(),
  },
}))

// Mock workout store
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(),
}))

describe('useLoginPrefetch', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })

    // Set up default mocks
    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    })
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([
      { Id: 123, Exercises: [] },
    ])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: vi.fn().mockResolvedValue(undefined),
      loadExerciseRecommendation: vi.fn().mockResolvedValue(undefined),
      setWorkout: vi.fn(),
      exercises: [],
    } as any)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  describe('Initial state', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

      expect(result.current.progress).toBe(0)
      expect(result.current.isComplete).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.status).toBe('Starting...')
      expect(result.current.programProgress).toBe(0)
      expect(result.current.isProgramError).toBe(false)
    })
  })

  describe('Prefetching', () => {
    it('should start user stats prefetch', () => {
      const mockUserStatsPrefetch = {
        prefetch: vi.fn(),
      }

      vi.mocked(usePrefetchUserStats).mockReturnValue(mockUserStatsPrefetch)

      const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

      act(() => {
        result.current.startPrefetch()
      })

      expect(mockUserStatsPrefetch.prefetch).toHaveBeenCalledTimes(1)
    })

    it('should update progress when prefetch starts', () => {
      const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

      act(() => {
        result.current.startPrefetch()
      })

      // Progress should be 25 after starting (average of 50 + 0)
      expect(result.current.progress).toBe(25)
      expect(result.current.status).toBe('Loading stats...')
    })

    it('should complete after timeout', async () => {
      const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

      act(() => {
        result.current.startPrefetch()
      })

      // Initially not complete
      expect(result.current.isComplete).toBe(false)
      expect(result.current.progress).toBe(25)

      // Advance timers by 150ms to trigger completion
      act(() => {
        vi.advanceTimersByTime(150)
      })

      // Should be complete now
      expect(result.current.isComplete).toBe(true)
      expect(result.current.progress).toBe(100)
      expect(result.current.status).toBe('Ready!')
    })
  })

  describe('Reset functionality', () => {
    it('should reset state', async () => {
      const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

      // Start prefetch
      act(() => {
        result.current.startPrefetch()
      })

      // Advance timers to complete
      act(() => {
        vi.advanceTimersByTime(150)
      })

      expect(result.current.isComplete).toBe(true)

      // Reset
      act(() => {
        result.current.reset()
      })

      expect(result.current.progress).toBe(0)
      expect(result.current.isComplete).toBe(false)
      expect(result.current.status).toBe('Starting...')
    })
  })

  describe('Backwards compatibility', () => {
    it('should maintain programProgress for compatibility', () => {
      const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

      expect(result.current.programProgress).toBe(result.current.progress)

      act(() => {
        result.current.startPrefetch()
      })

      expect(result.current.programProgress).toBe(result.current.progress)
    })

    it('should always return false for isProgramError', () => {
      const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

      expect(result.current.isProgramError).toBe(false)

      act(() => {
        result.current.startPrefetch()
      })

      expect(result.current.isProgramError).toBe(false)
    })
  })
})
