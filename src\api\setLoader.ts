/**
 * Progressive Set Loader
 *
 * Implements progressive loading of sets for exercises with retry logic,
 * caching, and batch loading support. This enables the app to show
 * exercises immediately while loading their sets in the background.
 */

import { workoutApi } from '@/api/workouts'
import type { WorkoutLogSerieModel } from '@/types'

interface CachedSets {
  sets: WorkoutLogSerieModel[]
  timestamp: number
}

export interface BatchLoadResult {
  success: boolean
  sets: WorkoutLogSerieModel[]
  error: string | null
}

export class SetLoader {
  private cache: Map<number, CachedSets>

  private ttl: number = 5 * 60 * 1000 // 5 minutes

  private maxRetries: number = 3

  private baseDelay: number = 1000

  private maxDelay: number = 30000

  private jitterFactor: number = 0.2

  // Track pending requests to prevent duplicate API calls
  private pendingRequests: Map<number, Promise<WorkoutLogSerieModel[]>>

  // Track abort controllers for cancellation
  private abortControllers: Map<number, AbortController>

  // Track failed requests for retry
  private failedRequests: Set<number>

  // Track loading states
  private loadingStates: Set<number>

  constructor() {
    this.cache = new Map()
    this.pendingRequests = new Map()
    this.abortControllers = new Map()
    this.failedRequests = new Set()
    this.loadingStates = new Set()
  }

  /**
   * Load sets for a single exercise with retry logic and caching
   */
  async loadExerciseSets(
    exerciseId: number,
    forceRefresh: boolean = false
  ): Promise<WorkoutLogSerieModel[]> {
    // Check cache first unless force refresh
    if (!forceRefresh) {
      const cached = this.cache.get(exerciseId)
      if (cached && Date.now() - cached.timestamp < this.ttl) {
        return cached.sets
      }
    }

    // Check if there's already a pending request for this exercise
    const existingRequest = this.pendingRequests.get(exerciseId)
    if (existingRequest) {
      return existingRequest
    }

    // Create abort controller for this request
    const abortController = new AbortController()
    this.abortControllers.set(exerciseId, abortController)

    // Mark as loading
    this.markAsLoading(exerciseId)

    // Create new request promise
    const requestPromise = this.loadWithRetryAndCache(
      exerciseId,
      abortController.signal
    )

    // Track the pending request
    this.pendingRequests.set(exerciseId, requestPromise)

    try {
      const result = await requestPromise
      return result
    } catch (error) {
      // Handle abort error
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request aborted')
      }

      // Mark as failed for other errors
      this.markAsFailed(exerciseId)
      throw error
    } finally {
      // Remove from pending requests and abort controllers when done
      this.pendingRequests.delete(exerciseId)
      this.abortControllers.delete(exerciseId)
      this.loadingStates.delete(exerciseId)
    }
  }

  /**
   * Load sets with retry logic and cache the result
   */
  private async loadWithRetryAndCache(
    exerciseId: number,
    signal?: AbortSignal
  ): Promise<WorkoutLogSerieModel[]> {
    // Load with retry logic
    const sets = await this.loadWithRetry(exerciseId, signal)

    // Cache the result only on success
    this.cache.set(exerciseId, {
      sets,
      timestamp: Date.now(),
    })

    return sets
  }

  /**
   * Batch load sets for multiple exercises
   */
  async batchLoadExerciseSets(
    exerciseIds: number[]
  ): Promise<Record<number, BatchLoadResult>> {
    const results: Record<number, BatchLoadResult> = {}

    // Process all exercises in parallel
    await Promise.all(
      exerciseIds.map(async (exerciseId) => {
        try {
          const sets = await this.loadExerciseSets(exerciseId)
          results[exerciseId] = {
            success: true,
            sets,
            error: null,
          }
        } catch (error) {
          results[exerciseId] = {
            success: false,
            sets: [],
            error: error instanceof Error ? error.message : 'Unknown error',
          }
        }
      })
    )
    return results
  }

  /**
   * Clear cache for a specific exercise
   */
  clearCache(exerciseId: number): void {
    this.cache.delete(exerciseId)
    // Also clear any pending request for this exercise
    this.pendingRequests.delete(exerciseId)
  }

  /**
   * Clear all cached data
   */
  clearAllCache(): void {
    this.cache.clear()
    this.pendingRequests.clear()
  }

  /**
   * Load sets with exponential backoff retry
   */
  private async loadWithRetry(
    exerciseId: number,
    signal?: AbortSignal
  ): Promise<WorkoutLogSerieModel[]> {
    let lastError: Error | null = null

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        // Check if aborted before making request
        if (signal?.aborted) {
          throw new Error('Request aborted')
        }

        // eslint-disable-next-line no-await-in-loop
        const sets = await workoutApi.getExerciseSets(exerciseId)

        // Handle null/undefined responses
        if (!sets) {
          return []
        }

        return sets
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')

        // Handle abort error
        if (signal?.aborted || lastError.message === 'Request aborted') {
          throw lastError
        }

        // Don't retry certain types of errors
        const errorMessage = lastError.message.toLowerCase()
        const isNonRetryableError =
          errorMessage.includes('404') ||
          errorMessage.includes('not found') ||
          errorMessage.includes('resource you are looking for has been removed')

        if (isNonRetryableError) {
          // For non-retryable errors, return empty array instead of throwing
          // Non-retryable error for exercise ${exerciseId}: ${lastError.message}
          return []
        }

        // Don't retry on the last attempt
        if (attempt < this.maxRetries) {
          const delay = this.calculateBackoffDelay(attempt)
          // eslint-disable-next-line no-await-in-loop
          await this.sleep(delay)
        }
      }
    }

    // All retries failed
    throw lastError || new Error('Failed to load exercise sets')
  }

  /**
   * Calculate exponential backoff delay with jitter
   */
  private calculateBackoffDelay(attempt: number): number {
    // Exponential backoff: baseDelay * (2 ^ attempt)
    const exponentialDelay = Math.min(
      this.baseDelay * Math.pow(2, attempt),
      this.maxDelay
    )

    // Add jitter: ±20% of calculated delay
    const jitter = exponentialDelay * this.jitterFactor
    const jitterAmount = (Math.random() * 2 - 1) * jitter

    return Math.max(0, exponentialDelay + jitterAmount)
  }

  /**
   * Sleep for a given duration
   */
  // eslint-disable-next-line class-methods-use-this
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(resolve, ms)
    })
  }

  /**
   * Cancel all pending requests
   */
  cancelPendingRequests(): void {
    // Abort all pending requests
    this.abortControllers.forEach((controller) => {
      controller.abort()
    })

    // Clear all tracking maps
    this.pendingRequests.clear()
    this.abortControllers.clear()
    this.loadingStates.clear()
  }

  /**
   * Clear stuck loading states
   */
  clearStuckStates(): void {
    // Clear cache entries that are marked as loading
    this.loadingStates.forEach((exerciseId) => {
      this.cache.delete(exerciseId)
    })

    // Clear the loading states set
    this.loadingStates.clear()
  }

  /**
   * Retry all failed requests
   */
  async retryFailed(): Promise<
    Array<{
      exerciseId: number
      success: boolean
      sets?: WorkoutLogSerieModel[]
      error?: string
    }>
  > {
    let results: Array<{
      exerciseId: number
      success: boolean
      sets?: WorkoutLogSerieModel[]
      error?: string
    }> = []

    // Get all failed exercise IDs
    const failedIds = Array.from(this.failedRequests)

    // Clear the failed set before retrying
    this.failedRequests.clear()

    // Retry each failed request
    const retryPromises = failedIds.map(async (exerciseId) => {
      try {
        const sets = await this.loadExerciseSets(exerciseId, true)
        return {
          exerciseId,
          success: true,
          sets,
        }
      } catch (error) {
        // Re-add to failed set if retry fails
        this.failedRequests.add(exerciseId)
        return {
          exerciseId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        }
      }
    })

    results = await Promise.all(retryPromises)

    return results
  }

  /**
   * Mark an exercise as loading
   */
  markAsLoading(exerciseId: number): void {
    this.loadingStates.add(exerciseId)
  }

  /**
   * Mark an exercise as failed
   */
  markAsFailed(exerciseId: number): void {
    this.failedRequests.add(exerciseId)
  }

  /**
   * Get the count of pending requests
   */
  getPendingRequestCount(): number {
    return this.pendingRequests.size
  }
}
