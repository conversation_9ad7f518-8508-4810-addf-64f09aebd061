/**
 * Tests for LocalStorageAdapter
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { LocalStorageAdapter } from '../LocalStorageAdapter'
import { TestDataGenerators } from '../../__tests__/test-utils'
import { CACHE_ENTRY_VERSION } from '../../types'

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {}
  
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      // Simulate quota exceeded for very large values
      if (value.length > 100000) {
        const error = new DOMException('QuotaExceededError')
        error.code = 22
        error.name = 'QuotaExceededError'
        throw error
      }
      store[key] = value
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      store = {}
    }),
    get length() {
      return Object.keys(store).length
    },
    key: vi.fn((index: number) => {
      const keys = Object.keys(store)
      return keys[index] || null
    }),
  }
})()

// Mock window.localStorage
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
})

describe('LocalStorageAdapter', () => {
  let adapter: LocalStorageAdapter

  beforeEach(() => {
    // Clear mock localStorage
    mockLocalStorage.clear()
    vi.clearAllMocks()
    
    adapter = new LocalStorageAdapter({
      keyPrefix: 'test-cache:',
      enableCompression: false, // Disable for easier testing
    })
  })

  afterEach(() => {
    mockLocalStorage.clear()
  })

  describe('Basic Operations', () => {
    it('should store and retrieve values', async () => {
      const key = 'test-key'
      const value = { data: 'test-value' }
      const metadata = TestDataGenerators.createMetadata()

      await adapter.set(key, value, metadata)
      const result = await adapter.get(key)

      expect(result).not.toBeNull()
      expect(result?.value).toEqual(value)
      expect(result?.metadata.namespace).toBe(metadata.namespace)
    })

    it('should return null for non-existent keys', async () => {
      const result = await adapter.get('non-existent')
      expect(result).toBeNull()
    })

    it('should delete values', async () => {
      const key = 'test-key'
      const value = 'test-value'
      const metadata = TestDataGenerators.createMetadata()

      await adapter.set(key, value, metadata)
      await adapter.delete(key)
      
      const result = await adapter.get(key)
      expect(result).toBeNull()
    })

    it('should clear all values with prefix', async () => {
      const metadata = TestDataGenerators.createMetadata()
      
      await adapter.set('key1', 'value1', metadata)
      await adapter.set('key2', 'value2', metadata)
      
      // Add some non-prefixed keys
      mockLocalStorage.setItem('other-key', 'other-value')

      await adapter.clear()

      expect(await adapter.get('key1')).toBeNull()
      expect(await adapter.get('key2')).toBeNull()
      expect(mockLocalStorage.getItem('other-key')).toBe('other-value')
    })
  })

  describe('TTL and Expiration', () => {
    it('should return null for expired entries', async () => {
      const key = 'expired-key'
      const value = 'test-value'
      const expiredMetadata = TestDataGenerators.createMetadata({
        expires: Date.now() - 1000, // Expired 1 second ago
      })

      await adapter.set(key, value, expiredMetadata)
      const result = await adapter.get(key)

      expect(result).toBeNull()
    })

    it('should clean up expired entries on get', async () => {
      const key = 'expired-key'
      const value = 'test-value'
      const expiredMetadata = TestDataGenerators.createMetadata({
        expires: Date.now() - 1000,
      })

      await adapter.set(key, value, expiredMetadata)
      await adapter.get(key) // This should trigger cleanup

      const keys = await adapter.getAllKeys()
      expect(keys).not.toContain(key)
    })
  })

  describe('Key Management', () => {
    it('should return all valid keys', async () => {
      const metadata = TestDataGenerators.createMetadata()
      
      await adapter.set('key1', 'value1', metadata)
      await adapter.set('key2', 'value2', metadata)

      const keys = await adapter.getAllKeys()

      expect(keys).toHaveLength(2)
      expect(keys).toContain('key1')
      expect(keys).toContain('key2')
    })

    it('should exclude expired keys from getAllKeys', async () => {
      const validMetadata = TestDataGenerators.createMetadata()
      const expiredMetadata = TestDataGenerators.createMetadata({
        expires: Date.now() - 1000,
      })

      await adapter.set('valid-key', 'value1', validMetadata)
      await adapter.set('expired-key', 'value2', expiredMetadata)

      const keys = await adapter.getAllKeys()

      expect(keys).toContain('valid-key')
      expect(keys).not.toContain('expired-key')
    })

    it('should use correct key prefix', async () => {
      const metadata = TestDataGenerators.createMetadata()
      await adapter.set('test-key', 'test-value', metadata)

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'test-cache:test-key',
        expect.any(String)
      )
    })
  })

  describe('Size Tracking', () => {
    it('should calculate total size', async () => {
      const metadata = TestDataGenerators.createMetadata()
      
      await adapter.set('key1', 'value1', metadata)
      await adapter.set('key2', 'value2', metadata)

      const size = await adapter.getSize()
      expect(size).toBeGreaterThan(0)
    })
  })

  describe('Quota Handling', () => {
    it('should handle quota exceeded errors', async () => {
      const key = 'large-key'
      const largeValue = 'x'.repeat(200000) // This will trigger quota exceeded
      const metadata = TestDataGenerators.createMetadata()

      // Should not throw, but handle gracefully
      await expect(adapter.set(key, largeValue, metadata))
        .rejects.toThrow('localStorage quota exceeded')
    })

    it('should clear expired entries when quota exceeded', async () => {
      // First, add some expired entries
      const expiredMetadata = TestDataGenerators.createMetadata({
        expires: Date.now() - 1000,
      })
      
      await adapter.set('expired1', 'value1', expiredMetadata)
      await adapter.set('expired2', 'value2', expiredMetadata)

      // Now try to add a large value that would exceed quota
      const largeValue = 'x'.repeat(200000)
      const metadata = TestDataGenerators.createMetadata()

      try {
        await adapter.set('large-key', largeValue, metadata)
      } catch (error) {
        // Expected to fail, but expired entries should be cleaned
      }

      // Verify expired entries were cleaned
      const keys = await adapter.getAllKeys()
      expect(keys).not.toContain('expired1')
      expect(keys).not.toContain('expired2')
    })
  })

  describe('Storage Statistics', () => {
    it('should provide storage statistics', async () => {
      const metadata = TestDataGenerators.createMetadata()
      
      await adapter.set('key1', 'value1', metadata)
      await adapter.set('key2', 'value2', metadata)

      const stats = await adapter.getStorageStats()

      expect(stats.totalEntries).toBe(2)
      expect(stats.totalSize).toBeGreaterThan(0)
      expect(stats).toHaveProperty('availableSpace')
      expect(stats).toHaveProperty('quotaUsage')
    })
  })

  describe('Static Methods', () => {
    it('should check if localStorage is available', () => {
      expect(LocalStorageAdapter.isAvailable()).toBe(true)
    })

    it('should clear all data with prefix', () => {
      mockLocalStorage.setItem('test-cache:key1', 'value1')
      mockLocalStorage.setItem('test-cache:key2', 'value2')
      mockLocalStorage.setItem('other:key3', 'value3')

      LocalStorageAdapter.clearAll('test-cache:')

      expect(mockLocalStorage.getItem('test-cache:key1')).toBeNull()
      expect(mockLocalStorage.getItem('test-cache:key2')).toBeNull()
      expect(mockLocalStorage.getItem('other:key3')).toBe('value3')
    })
  })

  describe('Metadata Operations', () => {
    it('should get all keys with metadata', async () => {
      const metadata1 = TestDataGenerators.createMetadata({ namespace: 'ns1' })
      const metadata2 = TestDataGenerators.createMetadata({ namespace: 'ns2' })
      
      await adapter.set('key1', 'value1', metadata1)
      await adapter.set('key2', 'value2', metadata2)

      const keysWithMetadata = await adapter.getAllKeysWithMetadata()

      expect(keysWithMetadata).toHaveLength(2)
      expect(keysWithMetadata[0]).toHaveProperty('key')
      expect(keysWithMetadata[0]).toHaveProperty('metadata')
      expect(keysWithMetadata[0].metadata.version).toBe(CACHE_ENTRY_VERSION)
    })
  })

  describe('Cleanup', () => {
    it('should cleanup expired entries', async () => {
      const validMetadata = TestDataGenerators.createMetadata()
      const expiredMetadata = TestDataGenerators.createMetadata({
        expires: Date.now() - 1000,
      })

      await adapter.set('valid-key', 'value1', validMetadata)
      await adapter.set('expired-key1', 'value2', expiredMetadata)
      await adapter.set('expired-key2', 'value3', expiredMetadata)

      const cleanedCount = await adapter.cleanup()

      expect(cleanedCount).toBe(2)
      expect(await adapter.get('valid-key')).not.toBeNull()
      expect(await adapter.get('expired-key1')).toBeNull()
      expect(await adapter.get('expired-key2')).toBeNull()
    })
  })

  describe('Batch Operations', () => {
    it('should not support batch operations', () => {
      expect(adapter.supportsBatch()).toBe(false)
    })
  })

  describe('Error Handling', () => {
    it('should handle localStorage unavailable', () => {
      // Mock localStorage as unavailable
      Object.defineProperty(window, 'localStorage', {
        value: undefined,
        writable: true,
      })

      expect(() => new LocalStorageAdapter()).toThrow('localStorage is not available')

      // Restore localStorage
      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      })
    })

    it('should handle serialization errors', async () => {
      const circularObj: any = {}
      circularObj.self = circularObj
      const metadata = TestDataGenerators.createMetadata()

      await expect(adapter.set('circular', circularObj, metadata))
        .rejects.toThrow('Failed to serialize')
    })
  })
})
