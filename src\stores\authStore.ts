import { create } from 'zustand'
import { persist, devtools } from 'zustand/middleware'
import type { LoginSuccessResult, LoginSuccessResultAlt } from '@/types'
import type {
  AuthState,
  User,
  CachedUserInfo,
  UserInfoCache,
} from './authStore/types'
import {
  CACHE_VERSION,
  USER_INFO_CACHE_TTL,
  SESSION_TIMEOUT,
  initialState,
} from './authStore/constants'
import { setToken, clearToken, updateTokenInClient } from '@/utils/tokenManager'
import { debugLog } from '@/utils/debugLog'
import { performCompleteLogout } from '@/utils/logout'

export type {
  User,
  CachedUserInfo,
  UserInfoCache,
  AuthState,
} from './authStore/types'

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        setAuth: async (
          data: LoginSuccessResult | LoginSuccessResultAlt,
          originalEmail?: string
        ) => {
          let user: User
          let token: string
          let refreshToken: string

          // Handle different response formats
          if (data && typeof data === 'object' && 'UserData' in data) {
            // Alternative format (used in tests)
            user = {
              email: data.UserData.Email,
              name: data.UserData.Name,
            }
            token = data.UserToken
            refreshToken = data.RefreshToken
          } else {
            // Standard OAuth format
            // Note: Dr. Muscle API sometimes doesn't return userName field
            // In that case, use the original email from login credentials
            const email = data.userName || originalEmail
            if (!email) {
              throw new Error(
                'No email available from login response or credentials'
              )
            }

            user = {
              email,
            }
            token = data.access_token
            // Dr. Muscle API doesn't return a separate refresh token
            // The access token is used for both access and refresh
            refreshToken = data.access_token
          }

          // Set the Authorization header in the API client
          // This is critical for Dr. Muscle API authentication
          setToken(token)
          updateTokenInClient(token)

          // Exchange tokens for httpOnly cookies
          try {
            const response = await fetch('/api/auth/exchange', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ token, refreshToken }),
              credentials: 'include', // Ensure cookies are sent/received
            })

            if (!response.ok) {
              debugLog.warn(
                '[Auth] Failed to exchange tokens for cookies, continuing with login'
              )
            }
          } catch (error) {
            // Continue to set state even if cookie exchange fails
            debugLog.warn(
              '[Auth] Cookie exchange failed, continuing with login:',
              error
            )
          }

          set({
            user,
            token,
            refreshToken,
            isAuthenticated: true,
            error: null,
            lastActivity: Date.now(),
          })
        },

        logout: async () => {
          // Clear Authorization header from API client
          clearToken()
          updateTokenInClient(null)

          // Clear auth cookies
          try {
            await fetch('/api/auth/exchange', {
              method: 'DELETE',
              credentials: 'include', // Ensure cookies are sent/received
            })
          } catch (error) {
            // Continue even if cookie clear fails
            debugLog.warn(
              '[Auth] Failed to clear cookies during logout:',
              error
            )
          }

          // Perform complete logout clearing all caches and stores
          try {
            await performCompleteLogout()
          } catch (error) {
            // Continue with logout even if cache clearing fails
            debugLog.warn('[Auth] Failed to clear caches during logout:', error)
          }

          set((state) => ({
            ...initialState,
            hasHydrated: state.hasHydrated, // Keep hydrated state
            cacheVersion: CACHE_VERSION, // Keep current cache version
            cachedUserInfo: null, // Explicitly clear cached user info
          }))
        },

        updateTokens: async (token: string, refreshToken: string) => {
          // Update Authorization header in API client
          setToken(token)
          updateTokenInClient(token)

          set({
            token,
            refreshToken,
          })
        },

        updateUser: (userData: Partial<User>) => {
          set((state) => ({
            user: state.user ? { ...state.user, ...userData } : null,
          }))
        },

        setUser: (user: User) => {
          set({ user })
        },

        setError: (error: string) => {
          set({
            error,
            isLoading: false,
          })
        },

        clearError: () => {
          set({ error: null })
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading })
        },

        setHasHydrated: (hasHydrated: boolean) => {
          set({ hasHydrated })
        },

        updateLastActivity: () => {
          set({ lastActivity: Date.now() })
        },

        checkSessionTimeout: () => {
          const state = get()
          if (!state.isAuthenticated) return

          const now = Date.now()
          const timeSinceLastActivity = now - state.lastActivity

          if (timeSinceLastActivity > SESSION_TIMEOUT) {
            // Session expired due to inactivity
            state.logout()
          }
        },

        // Cache Actions
        setCachedUserInfo: (data: CachedUserInfo) => {
          const cache: UserInfoCache = {
            data,
            timestamp: Date.now(),
            version: CACHE_VERSION,
          }
          set({ cachedUserInfo: cache })
        },

        getCachedUserInfo: () => {
          const state = get()

          // Check cache version
          if (state.cachedUserInfo?.version !== CACHE_VERSION) {
            return null
          }

          // Check if cache is stale
          if (state.isCacheStale()) {
            return null
          }

          return state.cachedUserInfo?.data || null
        },

        isCacheStale: () => {
          const state = get()

          if (!state.cachedUserInfo) {
            return true
          }

          const now = Date.now()
          const cacheAge = now - state.cachedUserInfo.timestamp

          return cacheAge > USER_INFO_CACHE_TTL
        },

        clearUserInfoCache: () => {
          set({ cachedUserInfo: null })
        },

        restoreAuthFromCookies: async (
          token: string,
          user: { email: string; name?: string } | null
        ) => {
          // Don't restore if token is missing
          if (!token) {
            return
          }

          // Set the Authorization header in the API client
          setToken(token)
          updateTokenInClient(token)

          // Update the auth state
          set({
            token,
            refreshToken: token, // Dr. Muscle uses same token for both
            isAuthenticated: true,
            user: user
              ? {
                  email: user.email,
                  name: user.name,
                }
              : null,
            error: null,
            lastActivity: Date.now(),
          })

          debugLog('[Auth] Restored auth state from cookies', {
            hasUser: !!user,
            userEmail: user?.email,
          })
        },
      }),
      {
        name: 'drmuscle-auth',
        partialize: (state) => ({
          user: state.user,
          // Do not persist tokens to localStorage for security
          // Tokens are stored in httpOnly cookies instead
          isAuthenticated: state.isAuthenticated,
          cachedUserInfo: state.cachedUserInfo,
          cacheVersion: state.cacheVersion,
        }),
        onRehydrateStorage: () => (state) => {
          state?.setHasHydrated(true)

          // If we have a persisted token, set it in the API client
          if (state?.token) {
            setToken(state.token)
            updateTokenInClient(state.token)
          }
        },
      }
    )
  )
)
