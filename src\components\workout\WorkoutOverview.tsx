'use client'

import { useEffect, useState, useCallback } from 'react'
import { useWorkout } from '@/hooks/useWorkout'
import { useProgressivePrefetch } from '@/hooks/useProgressivePrefetch'
import { useExerciseSetsPrefetch } from '@/hooks/useExerciseSetsPrefetch'
import { usePullToRefresh } from '@/hooks/usePullToRefresh'
import { useWorkoutRetry } from '@/hooks/useWorkoutRetry'
import { useWorkoutPreview } from '@/hooks/useWorkoutPreview'
import { useVisibilityChange } from '@/hooks/useVisibilityChange'
import { useWorkoutStore } from '@/stores/workoutStore'
import { PullToRefreshIndicator } from '@/components/PullToRefreshIndicator'
import { PULL_TO_REFRESH_CONFIG } from '@/config/pullToRefresh'
import { FloatingCTAButton } from '@/components/ui'
import { PerformanceMonitor, PerformanceMarks } from '@/utils/performance'
import { debugLog } from '@/utils/debugLog'
import { useNavigation } from '@/contexts/NavigationContext'
import {
  WorkoutLoadingState,
  WorkoutErrorState,
  NoWorkoutState,
  StatusIndicators,
} from './WorkoutOverviewStates'
import { useWorkoutActions } from './WorkoutOverviewActions'
import { TestV2UIBanner } from './TestV2UIBanner'
import { ExerciseList } from './ExerciseList'

export function WorkoutOverview() {
  const [isLoadingRecommendation, setIsLoadingRecommendation] = useState(false)
  const { setTitle } = useNavigation()

  // Use progressive prefetch to load upcoming exercises as user progresses
  useProgressivePrefetch()

  // Get prefetch status for UI indicators
  const {
    prefetchStatus,
    isPrefetching,
    prefetchedExerciseIds,
    retryCount: prefetchRetryCount,
  } = useExerciseSetsPrefetch()
  const {
    todaysWorkout,
    isLoadingWorkout,
    workoutError,
    startWorkout: startWorkoutFromHook,
    userProgramInfo,
    exercises,
    exerciseWorkSetsModels,
    expectedExerciseCount,
    hasInitialData,
    isOffline,
    refreshWorkout,
    updateExerciseWorkSets,
    workoutSession,
    finishWorkout,
    isLoading,
    loadExerciseRecommendation,
    workoutStorePrefetchStatus,
    workoutStorePrefetchedExerciseIds,
  } = useWorkout()

  // Use retry logic
  const { retryCount, isRetrying, handleManualRetry } = useWorkoutRetry({
    isLoading,
    isLoadingWorkout,
    todaysWorkout,
    workoutError,
    isOffline,
    refreshWorkout,
  })

  const {
    isStartingWorkout,
    handleStartWorkout,
    handleFinishWorkout,
    handleExerciseClick: handleExerciseClickOriginal,
    handleRetryExercise,
    hasCompletedSets,
    getButtonLabel,
    getButtonAriaLabel,
  } = useWorkoutActions({
    todaysWorkout,
    startWorkout: startWorkoutFromHook,
    exercises,
    workoutSession,
    loadExerciseRecommendation,
    updateExerciseWorkSets,
    finishWorkout,
  })

  // Use the original handleExerciseClick which now navigates to V2
  const handleExerciseClick = handleExerciseClickOriginal

  // Debug component lifecycle
  useEffect(() => {
    debugLog('[WorkoutOverview] Component mounted')

    return () => {
      debugLog('[WorkoutOverview] Component unmounting!')
    }
  }, [])

  // Mark when workout page becomes interactive
  useEffect(() => {
    if (!isLoadingWorkout && todaysWorkout) {
      PerformanceMonitor.mark(PerformanceMarks.WORKOUT_PAGE_INTERACTIVE)

      // Report metrics in development
      if (process.env.NODE_ENV === 'development') {
        PerformanceMonitor.reportKeyMetrics()
      }
    }
  }, [isLoadingWorkout, todaysWorkout])

  // Set navigation title to workout name when data is loaded
  useEffect(() => {
    const workoutGroup = todaysWorkout?.[0]
    const workout = workoutGroup?.WorkoutTemplates?.[0]

    if (workout?.Label) {
      setTitle(workout.Label)
    }

    // Clear title when unmounting
    return () => setTitle('')
  }, [todaysWorkout, setTitle])

  // Handle app visibility changes for background refresh
  const { handleAppBackground, handleAppForeground } = useWorkoutStore()

  useVisibilityChange(
    useCallback(
      (state: 'visible' | 'hidden') => {
        debugLog(`[WorkoutOverview] Visibility changed to: ${state}`)

        if (state === 'hidden') {
          handleAppBackground()
        } else {
          // handleAppForeground returns a Promise
          const result = handleAppForeground()
          if (result && typeof result.catch === 'function') {
            result.catch((error) => {
              debugLog(
                '[WorkoutOverview] Error during foreground refresh:',
                error
              )
            })
          }
        }
      },
      [handleAppBackground, handleAppForeground]
    )
  )

  // Pull-to-refresh functionality
  const pullToRefresh = usePullToRefresh({
    onRefresh: refreshWorkout,
    enabled: !isLoadingWorkout && hasInitialData,
    ...PULL_TO_REFRESH_CONFIG,
  })

  // Check if we have valid workout data structure
  const workoutGroup = todaysWorkout?.[0]
  const workout = workoutGroup?.WorkoutTemplates?.[0]

  // Use workout preview hook
  const { displayExercises, handleSkipExercise } = useWorkoutPreview(
    workout,
    workoutSession,
    exerciseWorkSetsModels
  )

  // Merge prefetch statuses from both sources
  const mergedPrefetchStatus = {
    ...workoutStorePrefetchStatus,
    ...prefetchStatus,
  }

  const mergedPrefetchedIds = Array.from(
    new Set([...workoutStorePrefetchedExerciseIds, ...prefetchedExerciseIds])
  )

  // Calculate prefetch statistics for status indicators
  const prefetchedCount = mergedPrefetchedIds.length
  const errorCount = Object.values(mergedPrefetchStatus).filter(
    (status) => status === 'error'
  ).length
  const loadingCount = Object.values(mergedPrefetchStatus).filter(
    (status) => status === 'loading'
  ).length
  const totalPrefetchAttempts = Object.keys(mergedPrefetchStatus).length
  const prefetchProgress =
    totalPrefetchAttempts > 0
      ? Math.round(
          ((prefetchedCount + errorCount) / totalPrefetchAttempts) * 100
        )
      : undefined

  // Show skeleton only when no workout data is available at all
  if ((isLoadingWorkout || isRetrying) && !todaysWorkout) {
    return <WorkoutLoadingState isStartingWorkout={isStartingWorkout} />
  }

  // Error state
  if (workoutError) {
    return <WorkoutErrorState error={workoutError} />
  }

  // No workout state - only show when we're sure there's no workout available
  // AND we've exhausted retry attempts or user is offline
  if (
    !isLoading &&
    !isLoadingWorkout &&
    (!todaysWorkout || todaysWorkout.length === 0 || !workout) &&
    (retryCount >= 3 || isOffline) &&
    !isRetrying
  ) {
    return (
      <NoWorkoutState
        todaysWorkout={todaysWorkout}
        isLoadingWorkout={isLoadingWorkout}
        hasInitialData={hasInitialData}
        userProgramInfo={userProgramInfo}
        onRetry={handleManualRetry}
        isOffline={isOffline}
      />
    )
  }

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    debugLog('[WorkoutOverview] Workout data check:', {
      hasTodaysWorkout: !!todaysWorkout,
      todaysWorkoutLength: todaysWorkout?.length,
      workoutGroup,
      hasWorkoutTemplates: !!workoutGroup?.WorkoutTemplates,
      workoutTemplatesLength: workoutGroup?.WorkoutTemplates?.length,
      workout,
    })
  }

  return (
    <div
      className="h-full flex flex-col bg-bg-primary relative"
      data-testid="workout-overview-container"
    >
      {/* Pull-to-refresh indicator */}
      <PullToRefreshIndicator
        pullDistance={pullToRefresh.pullDistance}
        threshold={PULL_TO_REFRESH_CONFIG.threshold}
        isRefreshing={pullToRefresh.isRefreshing}
        isPulling={pullToRefresh.isPulling}
      />
      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 pb-24">
          {/* Extra bottom padding for fixed button */}
          <div className="mx-auto max-w-lg">
            {/* Status indicators */}
            <StatusIndicators
              isOffline={isOffline}
              isRefreshing={pullToRefresh.isRefreshing}
              prefetchStatus={mergedPrefetchStatus}
              isPrefetching={isPrefetching || loadingCount > 0}
              prefetchedCount={prefetchedCount}
              errorCount={errorCount}
              prefetchProgress={loadingCount > 0 ? prefetchProgress : undefined}
              isSmartScheduling={workoutSession !== null || prefetchedCount > 0}
              retryCount={prefetchRetryCount}
            />

            {/* Test V2 UI Banner - Remove in production */}
            <TestV2UIBanner
              displayExercises={displayExercises}
              todaysWorkout={todaysWorkout}
              workoutSession={workoutSession}
              startWorkoutFromHook={startWorkoutFromHook}
              exercises={exercises}
              loadExerciseRecommendation={loadExerciseRecommendation}
              isStartingWorkout={isStartingWorkout}
              isLoadingRecommendation={isLoadingRecommendation}
              setIsLoadingRecommendation={setIsLoadingRecommendation}
            />

            {/* Exercise List */}
            <ExerciseList
              displayExercises={displayExercises}
              handleExerciseClick={handleExerciseClick}
              handleRetryExercise={handleRetryExercise}
              handleSkipExercise={handleSkipExercise}
              workout={workout}
              isLoadingWorkout={isLoadingWorkout}
              expectedExerciseCount={expectedExerciseCount}
            />
          </div>
        </div>
      </div>

      {/* Floating CTA Button */}
      <FloatingCTAButton
        onClick={
          workoutSession && hasCompletedSets
            ? handleFinishWorkout
            : handleStartWorkout
        }
        label={getButtonLabel()}
        ariaLabel={getButtonAriaLabel()}
        isLoading={isStartingWorkout}
        disabled={isStartingWorkout}
      />
    </div>
  )
}
