import { useState, useCallback, useRef } from 'react'
import { SetLoader } from '@/api/setLoader'
import { useRobustPrefetchErrorHandling } from './useRobustPrefetchErrorHandling'
import { usePrefetchMemoryOptimization } from './usePrefetchMemoryOptimization'

type PrefetchStatus = 'loading' | 'success' | 'error'

interface PrefetchState {
  [exerciseId: number]: PrefetchStatus
}

interface CacheEntry {
  exerciseId: number
  sets: unknown[]
  timestamp: number
  accessCount?: number
  lastAccessed?: number
}

interface UseExerciseSetsPrefetchReturn {
  prefetchStatus: PrefetchState
  isPrefetching: boolean
  prefetchedExerciseIds: number[]
  prefetchExerciseSets: (exerciseIds: number[]) => Promise<void>
  isExercisePrefetched: (exerciseId: number) => boolean
  clearPrefetchCache: () => void
  onProgress?: (progress: number) => void
  retryCount: number
  errorHistory: Array<{
    timestamp: number
    error: Error
    exerciseIds: number[]
    retryAttempt: number
  }>
  hasExceededMaxRetries: boolean
  isCircuitBreakerOpen: boolean
  // Memory optimization properties
  cacheSize: number
  memoryUsage: number
  isMemoryPressure: boolean
  shouldReducePrefetching: boolean
  recommendedBatchSize: number
}

export function useExerciseSetsPrefetch(): UseExerciseSetsPrefetchReturn {
  const [prefetchStatus, setPrefetchStatus] = useState<PrefetchState>({})
  const [isPrefetching, setIsPrefetching] = useState(false)
  const [prefetchedExerciseIds, setPrefetchedExerciseIds] = useState<number[]>(
    []
  )

  const setLoader = useRef(new SetLoader())
  const prefetchPromiseRef = useRef<Promise<void> | null>(null)
  const onProgressRef = useRef<((progress: number) => void) | undefined>(
    undefined
  )

  // Cache for prefetched data with memory optimization
  const prefetchCacheRef = useRef<Map<number, CacheEntry>>(new Map())

  // Memory optimization
  const {
    cacheSize,
    memoryUsage,
    isMemoryPressure,
    shouldReducePrefetching,
    recommendedBatchSize,
    recordCacheHit,
    recordCacheMiss,
    performMemoryCleanup,
  } = usePrefetchMemoryOptimization({
    prefetchedData: prefetchCacheRef.current,
    clearCache: () => {
      prefetchCacheRef.current.clear()
      setPrefetchStatus({})
      setPrefetchedExerciseIds([])
    },
    maxCacheSize: 50,
    maxCacheAge: 300000, // 5 minutes
    memoryThreshold: 100, // 100MB
    autoCleanupInterval: 60000, // 1 minute
  })

  // Core prefetch function without error handling
  const basePrefetchExerciseSets = useCallback(
    async (exerciseIds: number[]): Promise<void> => {
      // Check cache first and filter out already cached exercises
      const exercisesToFetch = exerciseIds.filter((id) => {
        const cached = prefetchCacheRef.current.get(id)
        if (cached) {
          // Update access info
          cached.lastAccessed = Date.now()
          cached.accessCount = (cached.accessCount || 0) + 1
          recordCacheHit()
          return false // Don't need to fetch
        }
        recordCacheMiss()
        return true // Need to fetch
      })

      // If all exercises are cached, no need to fetch
      if (exercisesToFetch.length === 0) {
        return
      }

      const results =
        await setLoader.current.batchLoadExerciseSets(exercisesToFetch)

      // Update cache and status based on results
      setPrefetchStatus((prev) => {
        const newStatus = { ...prev }
        Object.entries(results).forEach(([idStr, result]) => {
          const id = parseInt(idStr)
          newStatus[id] = result.success ? 'success' : 'error'

          // Add to cache if successful
          if (result.success) {
            prefetchCacheRef.current.set(id, {
              exerciseId: id,
              sets: (result as { data?: unknown[] }).data || [],
              timestamp: Date.now(),
              accessCount: 1,
              lastAccessed: Date.now(),
            })
          }
        })
        return newStatus
      })

      // Update prefetched IDs (only successful ones)
      const successfulIds = Object.entries(results)
        .filter(([, result]) => result.success)
        .map(([idStr]) => parseInt(idStr))

      setPrefetchedExerciseIds((prev) => {
        const newIds = [...new Set([...prev, ...successfulIds])]
        return newIds
      })

      // Call progress callback if set
      if (onProgressRef.current) {
        onProgressRef.current(1)
      }

      // Check if any failed and throw error to trigger retry
      const failedIds = Object.entries(results)
        .filter(([, result]) => !result.success)
        .map(([idStr]) => parseInt(idStr))

      if (failedIds.length > 0) {
        throw new Error(`Failed to prefetch exercises: ${failedIds.join(', ')}`)
      }
    },
    [recordCacheHit, recordCacheMiss]
  )

  // Robust error handling wrapper
  const {
    prefetchWithRetry,
    retryCount,
    errorHistory,
    hasExceededMaxRetries,
    isCircuitBreakerOpen,
  } = useRobustPrefetchErrorHandling({
    prefetchExerciseSets: basePrefetchExerciseSets,
    maxRetries: 3,
    retryDelayMs: 1000,
    exponentialBackoff: true,
    circuitBreakerThreshold: 5,
    circuitBreakerTimeoutMs: 30000,
    enableGracefulDegradation: true,
  })

  const prefetchExerciseSets = useCallback(
    async (exerciseIds: number[]) => {
      // Prevent duplicate prefetch requests
      if (isPrefetching && prefetchPromiseRef.current) {
        return prefetchPromiseRef.current
      }

      // Handle memory pressure by reducing batch size
      let adjustedExerciseIds = exerciseIds
      if (
        shouldReducePrefetching &&
        exerciseIds.length > recommendedBatchSize
      ) {
        adjustedExerciseIds = exerciseIds.slice(0, recommendedBatchSize)
        console.warn(
          `[useExerciseSetsPrefetch] Memory pressure detected, reducing batch size from ${exerciseIds.length} to ${recommendedBatchSize}`
        )
      }

      setIsPrefetching(true)

      // Mark all exercises as loading
      setPrefetchStatus((prev) => {
        const newStatus = { ...prev }
        adjustedExerciseIds.forEach((id) => {
          newStatus[id] = 'loading'
        })
        return newStatus
      })

      const prefetchPromise = (async () => {
        try {
          // Perform memory cleanup if under pressure
          if (isMemoryPressure) {
            performMemoryCleanup()
          }

          // Use robust error handling with retries
          await prefetchWithRetry(adjustedExerciseIds)
        } catch (error) {
          console.error('Error prefetching exercise sets after retries:', error)
          // Mark all as error - the robust handler already tried retries
          setPrefetchStatus((prev) => {
            const newStatus = { ...prev }
            adjustedExerciseIds.forEach((id) => {
              newStatus[id] = 'error'
            })
            return newStatus
          })
        } finally {
          setIsPrefetching(false)
          prefetchPromiseRef.current = null
        }
      })()

      prefetchPromiseRef.current = prefetchPromise
      return prefetchPromise
    },
    [
      isPrefetching,
      prefetchWithRetry,
      shouldReducePrefetching,
      recommendedBatchSize,
      isMemoryPressure,
      performMemoryCleanup,
    ]
  )

  const isExercisePrefetched = useCallback(
    (exerciseId: number): boolean => {
      return prefetchStatus[exerciseId] === 'success'
    },
    [prefetchStatus]
  )

  const clearPrefetchCache = useCallback(() => {
    setPrefetchStatus({})
    setPrefetchedExerciseIds([])
  }, [])

  return {
    prefetchStatus,
    isPrefetching,
    prefetchedExerciseIds,
    prefetchExerciseSets,
    isExercisePrefetched,
    clearPrefetchCache,
    retryCount,
    errorHistory,
    hasExceededMaxRetries,
    isCircuitBreakerOpen,
    // Memory optimization properties
    cacheSize,
    memoryUsage,
    isMemoryPressure,
    shouldReducePrefetching,
    recommendedBatchSize,
    get onProgress() {
      return onProgressRef.current
    },
    set onProgress(callback: ((progress: number) => void) | undefined) {
      onProgressRef.current = callback
    },
  }
}
