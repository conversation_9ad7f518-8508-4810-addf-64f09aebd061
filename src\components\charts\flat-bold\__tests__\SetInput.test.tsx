import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { SetInput } from '../SetInput'

describe('Flat-Bold SetInput Component', () => {
  it('should render large rectangular inputs', () => {
    render(
      <SetInput
        weight={100}
        reps={10}
        onWeightChange={() => {}}
        onRepsChange={() => {}}
      />
    )

    const inputs = screen.getAllByRole('spinbutton')
    expect(inputs).toHaveLength(2)

    inputs.forEach((input) => {
      expect(input).toHaveClass('h-16') // Large height
      expect(input).toHaveClass('text-3xl')
      expect(input).toHaveClass('font-bold')
    })
  })

  it('should have thick borders', () => {
    render(
      <SetInput
        weight={100}
        reps={10}
        onWeightChange={() => {}}
        onRepsChange={() => {}}
      />
    )

    const inputs = screen.getAllByRole('spinbutton')
    inputs.forEach((input) => {
      expect(input).toHaveClass('border-4')
      expect(input).toHaveClass('border-black')
    })
  })

  it('should have sharp corners', () => {
    render(
      <SetInput
        weight={100}
        reps={10}
        onWeightChange={() => {}}
        onRepsChange={() => {}}
      />
    )

    const inputs = screen.getAllByRole('spinbutton')
    inputs.forEach((input) => {
      expect(input).not.toHaveClass('rounded')
    })
  })

  it('should display labels', () => {
    render(
      <SetInput
        weight={100}
        reps={10}
        onWeightChange={() => {}}
        onRepsChange={() => {}}
      />
    )

    expect(screen.getByText('WEIGHT (LBS)')).toBeInTheDocument()
    expect(screen.getByText('REPS')).toBeInTheDocument()
  })

  it('should handle weight changes', () => {
    const handleWeightChange = vi.fn()
    render(
      <SetInput
        weight={100}
        reps={10}
        onWeightChange={handleWeightChange}
        onRepsChange={() => {}}
      />
    )

    const weightInput = screen.getByLabelText('WEIGHT (LBS)')
    fireEvent.change(weightInput, { target: { value: '120' } })

    expect(handleWeightChange).toHaveBeenCalledWith(120)
  })

  it('should handle reps changes', () => {
    const handleRepsChange = vi.fn()
    render(
      <SetInput
        weight={100}
        reps={10}
        onWeightChange={() => {}}
        onRepsChange={handleRepsChange}
      />
    )

    const repsInput = screen.getByLabelText('REPS')
    fireEvent.change(repsInput, { target: { value: '12' } })

    expect(handleRepsChange).toHaveBeenCalledWith(12)
  })

  it('should highlight on focus', () => {
    render(
      <SetInput
        weight={100}
        reps={10}
        onWeightChange={() => {}}
        onRepsChange={() => {}}
      />
    )

    const weightInput = screen.getByLabelText('WEIGHT (LBS)')
    expect(weightInput).toHaveClass('focus:border-[#00FF88]')
    expect(weightInput).toHaveClass('focus:outline-none')
  })

  it('should center text in inputs', () => {
    render(
      <SetInput
        weight={100}
        reps={10}
        onWeightChange={() => {}}
        onRepsChange={() => {}}
      />
    )

    const inputs = screen.getAllByRole('spinbutton')
    inputs.forEach((input) => {
      expect(input).toHaveClass('text-center')
    })
  })

  it('should be mobile-friendly with large touch targets', () => {
    render(
      <SetInput
        weight={100}
        reps={10}
        onWeightChange={() => {}}
        onRepsChange={() => {}}
      />
    )

    const inputs = screen.getAllByRole('spinbutton')
    inputs.forEach((input) => {
      expect(input).toHaveClass('min-w-[52px]')
      expect(input).toHaveClass('h-16') // 64px > 44px
    })
  })
})
