import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { getStatsConfig } from '../swipeableStatConfig'
import type { UserStats } from '@/types'

describe('swipeableStatConfig', () => {
  const mockStats: UserStats = {
    weekStreak: 5,
    workoutsCompleted: 20,
    lbsLifted: 10000,
    muscleStreak: 0,
    recoveryInfo: null,
  }

  describe('getStatsConfig', () => {
    it('should return valid SVG icons for all stats', () => {
      const config = getStatsConfig(mockStats, [0, 0, 0], false)

      // Test that we have 3 stats
      expect(config).toHaveLength(3)

      // Test workouts icon
      const workoutsIcon = render(config[0].icon as React.ReactElement)
      const workoutsSvg = workoutsIcon.container.querySelector('svg')
      expect(workoutsSvg).toBeTruthy()
      const workoutsPath = workoutsSvg?.querySelector('path')
      expect(workoutsPath?.getAttribute('d')).toBeTruthy()

      // Test week streak icon - ensure it has valid SVG path
      const weekStreakIcon = render(config[1].icon as React.ReactElement)
      const weekStreakSvg = weekStreakIcon.container.querySelector('svg')
      expect(weekStreakSvg).toBeTruthy()
      const weekStreakPath = weekStreakSvg?.querySelector('path')
      const weekStreakPathData = weekStreakPath?.getAttribute('d')
      expect(weekStreakPathData).toBeTruthy()
      // Check that the path is the fire/flame icon path
      expect(weekStreakPathData).toContain('M17.657 18.657')
      expect(weekStreakPathData).toContain('A8 8')

      // Test pounds lifted icon - should be a dumbbell, not a cloud
      const lbsIcon = render(config[2].icon as React.ReactElement)
      const lbsSvg = lbsIcon.container.querySelector('svg')
      expect(lbsSvg).toBeTruthy()
      const lbsPath = lbsSvg?.querySelector('path')
      const lbsPathData = lbsPath?.getAttribute('d')
      expect(lbsPathData).toBeTruthy()
      // Should not contain cloud path pattern (M3 15a4...)
      expect(lbsPathData).not.toMatch(/M3 15a4/)
    })

    it('should return correct labels in order', () => {
      const config = getStatsConfig(mockStats, [0, 0, 0], false)

      expect(config[0].label).toBe('Workouts')
      expect(config[1].label).toBe('Week streak')
      expect(config[2].label).toBe('lbs lifted') // Default mass unit is 'lbs'
    })

    it('should use real stats values when not using animated values', () => {
      const config = getStatsConfig(mockStats, [0, 0, 0], false)

      expect(config[0].value).toBe(20) // workoutsCompleted
      expect(config[1].value).toBe(5) // weekStreak
      expect(config[2].value).toBe(10000) // lbsLifted
    })

    it('should use animated values when specified', () => {
      const animatedValues = [10, 3, 5000]
      const config = getStatsConfig(mockStats, animatedValues, true)

      expect(config[0].value).toBe(10)
      expect(config[1].value).toBe(3)
      expect(config[2].value).toBe(5000)
    })

    it('should handle null stats gracefully', () => {
      const config = getStatsConfig(null, [1, 2, 3], false)

      expect(config[0].value).toBe(1) // falls back to animated value
      expect(config[1].value).toBe(2)
      expect(config[2].value).toBe(3)
    })

    it('should format pounds lifted with locale string', () => {
      const config = getStatsConfig(mockStats, [0, 0, 0], false)

      expect(config[2].formatter).toBeDefined()
      expect(config[2].formatter!(10000)).toBe('10,000')
    })
  })
})
