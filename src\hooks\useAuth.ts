import { useMutation } from '@tanstack/react-query'
import { useAuthStore } from '@/stores/authStore'
import { useProgramStore } from '@/stores/programStore'
import { authApi } from '@/api/auth'
import { userProfileApi } from '@/api/userProfile'
import { programApi } from '@/api/program'
import { startSession, endSession } from '@/utils/userInfoPerformance'
import { debugLog } from '@/utils/debugLog'
import type { LoginModel, LoginFormData } from '@/types'

/**
 * Custom hook for authentication operations
 * Integrates auth store with React Query for optimized state management
 */
export function useAuth() {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    setAuth,
    logout: storeLogout,
    setError,
    clearError,
  } = useAuthStore()

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginModel) => {
      clearError()
      return {
        data: await authApi.login(credentials),
        originalEmail: credentials.Username,
      }
    },
    onSuccess: async ({ data, originalEmail }) => {
      // Set auth immediately so userName (email) is available
      // Pass the original email in case the API response doesn't include userName
      setAuth(data, originalEmail)

      // Start smart prefetching in background
      // This runs during the success screen and improves perceived performance
      // We handle errors gracefully to avoid issues with new users
      const prefetchInBackground = async () => {
        // Small initial delay to ensure token is propagated
        await new Promise<void>((resolve) => setTimeout(resolve, 50))

        // Prefetch user info - silently skip on 404 (new users)
        try {
          const sessionId = startSession()
          const userInfo = await userProfileApi.getUserInfo()

          if (userInfo) {
            const { updateUser, setCachedUserInfo } = useAuthStore.getState()
            const userData = userInfo.Result || userInfo

            if (userData.FirstName || userData.LastName) {
              updateUser({
                firstName: userData.FirstName,
                lastName: userData.LastName,
              })
              setCachedUserInfo({
                firstName: userData.FirstName,
                lastName: userData.LastName,
                ...userData,
              })
            }
            endSession(sessionId, true)
          }
        } catch (error: unknown) {
          // Silently skip 404s (expected for new users)
          if (
            (error as { response?: { status?: number } })?.response?.status ===
            404
          ) {
            debugLog('[Login] User info not found - new user')
          } else if ((error as Error)?.message?.includes('network')) {
            debugLog.warn(
              '[Login] Prefetch network error:',
              (error as Error).message
            )
          }
          // Don't log other errors - they're not critical
        }

        // Small delay between requests
        await new Promise<void>((resolve) => setTimeout(resolve, 50))

        // Prefetch program data - silently skip if no data
        try {
          const { setCachedProgram } = useProgramStore.getState()
          const program = await programApi.getUserProgram()
          if (program) {
            setCachedProgram(program)
          }
        } catch (error: unknown) {
          // Only log real errors, not missing data
          if (
            (error as Error)?.message &&
            !(error as Error).message.includes('No program info')
          ) {
            debugLog.warn(
              '[Login] Program prefetch error:',
              (error as Error).message
            )
          }
        }

        // We skip progress and stats prefetch here - they'll be loaded on program page
        // This keeps login fast while still preloading the most important data
      }

      // Fire and forget - don't await
      prefetchInBackground().catch(() => {
        // Silently ignore any uncaught errors
      })
    },
    onError: (error: Error) => {
      // Only log detailed error info in development
      debugLog.error('Login error:', {
        name: error.name,
        message: error.message,
      })
      // Check if it's a network error from our interceptor
      if (error.name === 'NetworkError') {
        setError(error.message)
      } else if (error.message) {
        setError(error.message)
      } else {
        setError('Login failed. Please try again.')
      }
    },
  })

  // Logout mutation - no API call needed, just local cleanup
  const logoutMutation = useMutation({
    mutationFn: async () => {
      // No API call - Dr. Muscle backend doesn't have a logout endpoint
      // Just perform local logout
      return Promise.resolve()
    },
    onSuccess: () => {
      storeLogout()
    },
  })

  // Helper function to login with form data
  const login = async (formData: LoginModel | LoginFormData) => {
    // Handle both LoginModel and LoginFormData formats
    const credentials: LoginModel =
      'Username' in formData
        ? (formData as LoginModel)
        : {
            Username: (formData as LoginFormData).email,
            Password: (formData as LoginFormData).password,
          }

    return loginMutation.mutateAsync(credentials)
  }

  // Helper function to logout
  const logout = async () => {
    await logoutMutation.mutateAsync()
  }

  return {
    // State
    user,
    token,
    isAuthenticated,
    isLoading: loginMutation.isPending || logoutMutation.isPending || isLoading,
    error,

    // Actions
    login,
    logout,
    clearError,

    // Mutations (for direct access if needed)
    loginMutation,
    logoutMutation,
  }
}
