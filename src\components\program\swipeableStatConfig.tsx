import type { UserStats } from '@/types'

export interface StatConfig {
  value: number
  label: string
  icon: React.ReactNode
  formatter?: (value: number) => string
}

export function getStatsConfig(
  stats: UserStats | null,
  animatedValues: number[],
  useAnimatedValues: boolean,
  massUnit?: string
): StatConfig[] {
  return [
    {
      value: useAnimatedValues
        ? (animatedValues[0] ?? 0)
        : (stats?.workoutsCompleted ?? animatedValues[0] ?? 0),
      label: 'Workouts',
      icon: (
        <svg
          className="w-8 h-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
          />
        </svg>
      ),
    },
    {
      value: useAnimatedValues
        ? (animatedValues[1] ?? 0)
        : (stats?.weekStreak ?? animatedValues[1] ?? 0),
      label: 'Week streak',
      icon: (
        <svg
          className="w-8 h-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"
          />
        </svg>
      ),
    },
    {
      value: useAnimatedValues
        ? (animatedValues[2] ?? 0)
        : (stats?.lbsLifted ?? animatedValues[2] ?? 0),
      label: massUnit === 'kg' ? 'Kg Lifted' : 'Lbs Lifted',
      icon: (
        <svg
          className="w-8 h-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12l3.57-3.57L15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29z"
          />
        </svg>
      ),
      formatter: (value) => value.toLocaleString(),
    },
  ]
}
