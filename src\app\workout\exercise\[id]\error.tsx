'use client'

export default function ExerciseError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="flex items-center justify-center min-h-[100dvh] bg-bg-primary">
      <div className="text-center p-6 max-w-md">
        <h2 className="text-2xl font-bold text-error mb-4">
          Failed to Load Exercise
        </h2>
        <p className="text-text-secondary mb-6">
          {error.message || 'Something went wrong loading this exercise.'}
        </p>
        <div className="space-y-3">
          <button
            onClick={reset}
            className="w-full px-6 py-3 bg-brand-primary text-text-inverse rounded-lg font-semibold hover:bg-brand-primary/80 transition-colors min-h-[52px]"
          >
            Try Again
          </button>
          <button
            onClick={() => {
              window.location.href = '/workout'
            }}
            className="w-full px-6 py-3 bg-bg-secondary text-text-primary rounded-lg font-semibold hover:bg-bg-tertiary transition-colors min-h-[52px]"
          >
            Back to Workout
          </button>
        </div>
      </div>
    </div>
  )
}
