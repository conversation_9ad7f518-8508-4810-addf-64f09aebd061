'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useNavigation } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { useAuthStore } from '@/stores/authStore'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useExercisePageInitialization } from '@/hooks/useExercisePageInitialization'
import { useExerciseV2Actions } from '@/hooks/useExerciseV2Actions'
import { usePullToRefresh } from '@/hooks/usePullToRefresh'
import { PullToRefreshIndicator } from '@/components/PullToRefreshIndicator'
import { PULL_TO_REFRESH_CONFIG } from '@/config/pullToRefresh'
import { ExercisePageStates } from '@/components/workout-v2/ExercisePageStates'
import { ExerciseInfoHeader } from '@/components/workout-v2/ExerciseInfoHeader'
import { CurrentSetCard } from '@/components/workout-v2/CurrentSetCard'
import { TodaysSetsPreview } from '@/components/workout-v2/TodaysSetsPreview'
import { RestTimer } from '@/components/workout-v2/RestTimer'
import { RIRPicker } from '@/components/workout/RIRPicker'
import { ExerciseTransitionScreen } from '@/components/workout/ExerciseTransitionScreen'
import { generateAllSets } from '@/utils/generateAllSets'
import { debugLog } from '@/utils/debugLog'
import { getSetTypeFromSet } from '@/utils/getSetTypeFromSet'

interface ExercisePageV2ClientProps {
  exerciseId: number
}

export function ExercisePageV2Client({
  exerciseId,
}: ExercisePageV2ClientProps) {
  const router = useRouter()
  const { setTitle } = useNavigation()
  const { getCachedUserInfo } = useAuthStore()
  const userInfo = getCachedUserInfo()
  const unit = userInfo?.MassUnit === 'kg' ? 'kg' : 'lbs'
  const searchParams = useSearchParams()
  const [showTransition, setShowTransition] = useState(false)
  const [transitionComplete, setTransitionComplete] = useState(false)

  // Use workout hook for workout data
  const { isLoadingWorkout, workoutError, workoutSession } = useWorkout()
  const { loadingStates, restTimerState } = useWorkoutStore()

  // Initialize exercise page
  const { isInitializing, loadingError, retryInitialization } =
    useExercisePageInitialization(exerciseId)

  const {
    currentExercise,
    exercises,
    currentSetIndex,
    isSaving,
    saveError,
    showComplete,
    showExerciseComplete,
    recommendation,
    isLoading,
    error,
    isLastExercise,
    isLastSet,
    isWarmup,
    isFirstWorkSet,
    completedSets,
    setData,
    setSetData,
    setSaveError,
    handleSaveSet,
    refetchRecommendation,
    showRIRPicker,
    handleRIRSelect,
    handleRIRCancel,
  } = useSetScreenLogic(exerciseId, true)

  // Get exercise name from search params
  const exerciseNameFromParams = searchParams.get('exerciseName')

  // Determine if we should show transition
  useEffect(() => {
    // Show transition if:
    // 1. We have an exercise name from navigation
    // 2. No recommendation is loaded yet
    // 3. We haven't shown the transition already
    if (exerciseNameFromParams && !recommendation && !transitionComplete) {
      setShowTransition(true)
    }
  }, [exerciseNameFromParams, recommendation, transitionComplete])

  // Update navigation title with exercise name
  useEffect(() => {
    if (currentExercise) {
      setTitle(currentExercise.Label)
    }
    return () => setTitle('')
  }, [currentExercise, setTitle])

  // NOTE: Activity tracking removed to support indefinite login sessions

  // Pull-to-refresh functionality
  const pullToRefresh = usePullToRefresh({
    onRefresh: refetchRecommendation,
    enabled:
      !isInitializing && !isLoadingWorkout && !isSaving && !!recommendation,
    ...PULL_TO_REFRESH_CONFIG,
  })
  // Generate all sets
  const allSets = generateAllSets({
    recommendation,
    completedSets,
    currentSetIndex,
    setData,
    unit,
  })

  const currentSet = allSets.find((s) => s.IsNext)
  const totalSets = allSets.length // Include all sets (warmups + work sets)
  const completedCount = allSets.filter((s) => s.IsFinished).length // Include all completed sets
  const currentSetNumber = completedCount + 1

  debugLog('[ExercisePageV2Client] Exercise state:', {
    exerciseId,
    hasCurrentExercise: !!currentExercise,
    exerciseLabel: currentExercise?.Label,
    hasRecommendation: !!recommendation,
    allSetsCount: allSets.length,
    hasCurrentSet: !!currentSet,
    currentSetIndex,
    completedSetsCount: completedSets.length,
    showExerciseComplete,
    isExerciseFinished: currentExercise?.IsFinished,
  })

  // Extract action handlers to custom hook
  const { handleCompleteSet, handleSkipSet } = useExerciseV2Actions({
    currentExercise: currentExercise || null,
    workoutSession,
    setData,
    currentSetIndex,
    allSets,
    isWarmup,
    isLastSet,
    isFirstWorkSet,
    currentSet: currentSet || null,
    setSaveError,
    onExerciseComplete: () => {
      // This will trigger showExerciseComplete state
      handleSaveSet()
    },
    handleSaveSet,
  })

  // Navigation handler for exercise complete screen
  const handleExerciseCompleteNavigation = useCallback(() => {
    if (isLastExercise) {
      // Navigate to workout complete
      router.push('/workout/complete')
    } else {
      // Navigate to next exercise
      const currentIndex = exercises.findIndex((ex) => ex.Id === exerciseId)
      if (currentIndex !== -1 && currentIndex < exercises.length - 1) {
        const nextExercise = exercises[currentIndex + 1]
        if (nextExercise) {
          router.push(`/workout/exercise-v2/${nextExercise.Id}`)
        }
      }
    }
  }, [isLastExercise, exercises, exerciseId, router])

  debugLog('🏋️ [ExercisePageV2Client] Component rendered', {
    exerciseId,
    isLoadingWorkout,
    exercisesCount: exercises?.length || 0,
    isInitializing,
    hasLoadingError: !!loadingError,
    hasWorkoutError: !!workoutError,
    hasWorkoutSession: !!workoutSession,
    hasHandleCompleteSet: !!handleCompleteSet,
    hasCurrentExercise: !!currentExercise,
    currentExerciseId: currentExercise?.Id,
  })

  // Check if we're still loading recommendations for this exercise
  const isLoadingRecommendation = exerciseId
    ? loadingStates.get(exerciseId)
    : false

  debugLog('🎨 [ExercisePageV2Client] Rendering decision', {
    isInitializing,
    isLoadingWorkout,
    isLoadingRecommendation,
    hasRecommendation: !!recommendation,
    hasCurrentExercise: !!currentExercise,
    hasWorkoutSession: !!workoutSession,
    hasError: !!loadingError || !!workoutError,
  })

  // Show transition screen if needed
  if (showTransition && !transitionComplete) {
    return (
      <ExerciseTransitionScreen
        exerciseName={exerciseNameFromParams || 'Loading exercise...'}
        exerciseId={exerciseId}
        coordinatorEnabled
        onComplete={() => {
          setTransitionComplete(true)
          setShowTransition(false)
        }}
      />
    )
  }

  // Handle all state conditions with a dedicated component
  const stateComponent = (
    <ExercisePageStates
      loadingError={loadingError || null}
      workoutError={workoutError}
      retryInitialization={retryInitialization}
      isInitializing={isInitializing}
      isLoadingWorkout={isLoadingWorkout}
      isLoadingRecommendation={isLoadingRecommendation || false}
      isLoading={isLoading}
      recommendation={recommendation}
      currentExercise={currentExercise || null}
      workoutSession={workoutSession}
      error={error}
      refetchRecommendation={refetchRecommendation}
      showComplete={showComplete}
      showExerciseComplete={showExerciseComplete}
      currentSet={currentSet || null}
      isLastExercise={isLastExercise}
      handleSaveSet={handleSaveSet}
      handleNavigateToNext={handleExerciseCompleteNavigation}
      completedSets={completedSets}
    />
  )

  // Always return state component if we don't have the minimum required data
  // This prevents showing a blank/dark page during loading
  if (
    stateComponent.props.loadingError ||
    stateComponent.props.workoutError ||
    stateComponent.props.isInitializing ||
    stateComponent.props.isLoadingWorkout ||
    stateComponent.props.isLoadingRecommendation ||
    stateComponent.props.isLoading ||
    !stateComponent.props.recommendation ||
    !stateComponent.props.currentExercise ||
    !stateComponent.props.workoutSession ||
    stateComponent.props.error ||
    stateComponent.props.showComplete ||
    stateComponent.props.showExerciseComplete ||
    (stateComponent.props.recommendation && !stateComponent.props.currentSet) ||
    !currentSet || // Add explicit check for currentSet
    !allSets.length // Add explicit check for allSets
  ) {
    return stateComponent
  }

  return (
    <div
      className="flex flex-col min-h-screen bg-surface-primary relative"
      data-testid="exercise-page-container"
    >
      {/* Pull-to-refresh indicator */}
      <PullToRefreshIndicator
        pullDistance={pullToRefresh.pullDistance}
        threshold={PULL_TO_REFRESH_CONFIG.threshold}
        isRefreshing={pullToRefresh.isRefreshing}
        isPulling={pullToRefresh.isPulling}
      />

      {/* Exercise Info Header */}
      {currentExercise && currentSet && (
        <ExerciseInfoHeader
          currentSet={currentSetNumber}
          totalSets={totalSets}
          completedSets={completedCount}
          setType={getSetTypeFromSet(currentSet)}
        />
      )}

      {/* Main Content Area */}
      <div
        className={`flex-1 flex flex-col px-4 ${
          restTimerState.isActive ? 'pb-48' : ''
        }`}
      >
        {/* Hybrid View - Current set + Today's sets */}
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-center min-h-[300px]">
            <CurrentSetCard
              exercise={currentExercise || null}
              currentSet={currentSet || null}
              setData={setData}
              onSetDataChange={setSetData}
              onComplete={handleCompleteSet}
              onSkip={handleSkipSet}
              isSaving={isSaving}
              unit={unit}
              recommendation={recommendation}
              currentSetIndex={currentSetIndex}
              isWarmup={isWarmup}
              isFirstWorkSet={isFirstWorkSet}
            />
          </div>
          <div className="flex items-start justify-center pb-2">
            <TodaysSetsPreview allSets={allSets} unit={unit} />
          </div>
        </div>

        {/* Error message */}
        {saveError && <p className="text-error text-sm mt-2">{saveError}</p>}

        {/* Rest Timer at bottom (shows when active) */}
        <RestTimer />
      </div>

      {/* RIR Picker Modal */}
      <RIRPicker
        isOpen={showRIRPicker}
        onSelect={handleRIRSelect}
        onCancel={handleRIRCancel}
      />
    </div>
  )
}
