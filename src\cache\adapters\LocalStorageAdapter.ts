/**
 * LocalStorage cache adapter
 * 
 * This adapter uses the browser's localStorage API for persistent storage.
 * Features:
 * - Persistent storage across browser sessions
 * - Quota handling with automatic cleanup
 * - Compression for large values
 * - Available space checking
 */

import { BaseStorageAdapter } from './BaseStorageAdapter'
import { CacheQuotaExceededError } from '../types'

/**
 * Configuration for LocalStorage adapter
 */
interface LocalStorageAdapterConfig {
  /** Key prefix for isolation */
  keyPrefix?: string
  /** Whether to enable compression for large values */
  enableCompression?: boolean
  /** Compression threshold in bytes */
  compressionThreshold?: number
  /** Maximum number of entries to keep */
  maxEntries?: number
  /** Whether to enable automatic cleanup on quota exceeded */
  enableAutoCleanup?: boolean
}

/**
 * LocalStorage cache adapter
 */
export class LocalStorageAdapter extends BaseStorageAdapter {
  private readonly adapterConfig: Required<LocalStorageAdapterConfig>

  constructor(config: LocalStorageAdapterConfig = {}) {
    // Check if localStorage is available
    if (typeof window === 'undefined' || !window.localStorage) {
      throw new Error('localStorage is not available')
    }

    super(window.localStorage, {
      keyPrefix: config.keyPrefix ?? 'drmuscle-cache:',
      enableCompression: config.enableCompression ?? true,
      compressionThreshold: config.compressionThreshold ?? 1024,
    })

    this.adapterConfig = {
      keyPrefix: config.keyPrefix ?? 'drmuscle-cache:',
      enableCompression: config.enableCompression ?? true,
      compressionThreshold: config.compressionThreshold ?? 1024,
      maxEntries: config.maxEntries ?? 1000,
      enableAutoCleanup: config.enableAutoCleanup ?? true,
    }
  }

  /**
   * Enhanced quota handling with LRU eviction
   */
  protected async handleQuotaExceeded<T>(
    key: string,
    value: T,
    metadata: any
  ): Promise<void> {
    if (!this.adapterConfig.enableAutoCleanup) {
      throw new CacheQuotaExceededError(
        `localStorage quota exceeded for key "${key}"`
      )
    }

    try {
      // Step 1: Clear expired entries
      const expiredCleared = await this.clearExpiredEntries()
      
      if (expiredCleared > 0) {
        // Try again after clearing expired entries
        await this.trySetAfterCleanup(key, value, metadata)
        return
      }

      // Step 2: Check if we're over the entry limit
      const currentKeys = await this.getAllKeys()
      if (currentKeys.length >= this.adapterConfig.maxEntries) {
        // Evict LRU entries
        const evicted = await this.evictLRUEntries(Math.ceil(this.adapterConfig.maxEntries * 0.1))
        
        if (evicted > 0) {
          await this.trySetAfterCleanup(key, value, metadata)
          return
        }
      }

      // Step 3: Try compression if not already enabled
      if (!this.adapterConfig.enableCompression) {
        const entry = { value, metadata }
        const serialized = this.serializeEntry(entry)
        const compressed = this.compressValue(serialized)
        
        if (compressed.length < serialized.length * 0.8) { // 20% compression
          this.storage.setItem(this.buildKey(key), compressed)
          return
        }
      }

      // If all else fails, throw quota exceeded error
      throw new CacheQuotaExceededError(
        `localStorage quota exceeded for key "${key}" after cleanup attempts`
      )
    } catch (error) {
      if (error instanceof CacheQuotaExceededError) {
        throw error
      }
      throw new CacheQuotaExceededError(
        `localStorage quota exceeded for key "${key}"`,
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Try to set value after cleanup
   */
  private async trySetAfterCleanup<T>(
    key: string,
    value: T,
    metadata: any
  ): Promise<void> {
    const fullKey = this.buildKey(key)
    const entry = { value, metadata }
    const serialized = this.serializeEntry(entry)
    
    this.storage.setItem(fullKey, serialized)
  }

  /**
   * Evict least recently used entries
   */
  private async evictLRUEntries(count: number): Promise<number> {
    const entries: Array<{ key: string; accessed: number }> = []
    
    // Collect all entries with their access times
    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key && key.startsWith(this.config.keyPrefix)) {
        try {
          const serialized = this.storage.getItem(key)
          if (serialized) {
            const entry = this.deserializeEntry(serialized)
            entries.push({
              key,
              accessed: entry.metadata.accessed || 0,
            })
          }
        } catch {
          // Remove invalid entries
          entries.push({ key, accessed: 0 })
        }
      }
    }
    
    // Sort by access time (oldest first)
    entries.sort((a, b) => a.accessed - b.accessed)
    
    // Remove the oldest entries
    const toEvict = entries.slice(0, Math.min(count, entries.length))
    for (const { key } of toEvict) {
      this.storage.removeItem(key)
    }
    
    return toEvict.length
  }

  /**
   * Get storage statistics
   */
  async getStorageStats() {
    const stats = {
      totalEntries: 0,
      totalSize: 0,
      availableSpace: null as number | null,
      quotaUsage: null as number | null,
    }

    // Count entries and calculate size
    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key && key.startsWith(this.config.keyPrefix)) {
        stats.totalEntries++
        const value = this.storage.getItem(key)
        if (value) {
          stats.totalSize += this.calculateSize(value)
        }
      }
    }

    // Get available space if supported
    stats.availableSpace = await this.getAvailableSpace()

    // Calculate quota usage if available
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate()
        if (estimate.quota && estimate.usage) {
          stats.quotaUsage = estimate.usage / estimate.quota
        }
      } catch {
        // Ignore errors
      }
    }

    return stats
  }

  /**
   * Check if localStorage is available and working
   */
  static isAvailable(): boolean {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return false
      }

      // Test if we can actually use localStorage
      const testKey = '__localStorage_test__'
      window.localStorage.setItem(testKey, 'test')
      window.localStorage.removeItem(testKey)
      return true
    } catch {
      return false
    }
  }

  /**
   * Get estimated available space in localStorage
   */
  static async getAvailableSpace(): Promise<number | null> {
    if (!LocalStorageAdapter.isAvailable()) {
      return null
    }

    // Try to use Storage API if available
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate()
        if (estimate.quota && estimate.usage) {
          return estimate.quota - estimate.usage
        }
      } catch {
        // Fall through to manual estimation
      }
    }

    // Manual estimation by trying to store data
    try {
      const testKey = '__space_test__'
      const testData = 'x'.repeat(1024) // 1KB test
      let availableSpace = 0
      
      // Try increasing amounts until we hit quota
      for (let size = 1024; size <= 10 * 1024 * 1024; size *= 2) {
        try {
          const data = 'x'.repeat(size)
          window.localStorage.setItem(testKey, data)
          window.localStorage.removeItem(testKey)
          availableSpace = size
        } catch {
          break
        }
      }
      
      return availableSpace
    } catch {
      return null
    }
  }

  /**
   * Clear all localStorage data for this app
   */
  static clearAll(keyPrefix = 'drmuscle-cache:'): void {
    if (!LocalStorageAdapter.isAvailable()) {
      return
    }

    const keysToRemove: string[] = []
    
    for (let i = 0; i < window.localStorage.length; i++) {
      const key = window.localStorage.key(i)
      if (key && key.startsWith(keyPrefix)) {
        keysToRemove.push(key)
      }
    }
    
    for (const key of keysToRemove) {
      window.localStorage.removeItem(key)
    }
  }

  /**
   * Get all cache keys with their metadata
   */
  async getAllKeysWithMetadata() {
    const keys: Array<{ key: string; metadata: any }> = []
    
    for (let i = 0; i < this.storage.length; i++) {
      const fullKey = this.storage.key(i)
      if (fullKey && fullKey.startsWith(this.config.keyPrefix)) {
        try {
          const serialized = this.storage.getItem(fullKey)
          if (serialized) {
            const entry = this.deserializeEntry(serialized)
            keys.push({
              key: this.extractKey(fullKey),
              metadata: entry.metadata,
            })
          }
        } catch {
          // Skip invalid entries
        }
      }
    }
    
    return keys
  }

  /**
   * Cleanup expired entries and return count
   */
  async cleanup(): Promise<number> {
    return this.clearExpiredEntries()
  }
}
