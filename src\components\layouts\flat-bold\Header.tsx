import React from 'react'
import { cn } from '@/lib/utils'

interface ActionButton {
  icon: React.ReactNode
  onClick: () => void
  label?: string
}

interface HeaderProps {
  title: string
  subtitle?: string
  leftAction?: ActionButton
  rightAction?: ActionButton
  className?: string
}

export function Header({
  title,
  subtitle,
  leftAction,
  rightAction,
  className,
}: HeaderProps) {
  return (
    <header
      role="banner"
      className={cn(
        'h-16 w-full bg-black text-white',
        'flex items-center px-2',
        className
      )}
    >
      <div className="flex items-center justify-between w-full">
        {/* Left Action */}
        <div className="w-12">
          {leftAction && (
            <button
              onClick={leftAction.onClick}
              aria-label={leftAction.label}
              className="h-12 min-w-[52px] flex items-center justify-center text-white hover:bg-gray-800 transition-none"
            >
              {leftAction.icon}
            </button>
          )}
        </div>

        {/* Title Section */}
        <div
          className={cn(
            'flex-1 flex flex-col items-center',
            !leftAction && !rightAction && 'justify-center'
          )}
        >
          <h1 className="font-heading font-bold text-2xl uppercase">{title}</h1>
          {subtitle && <p className="text-[#00FF88] text-sm">{subtitle}</p>}
        </div>

        {/* Right Action */}
        <div className="w-12">
          {rightAction && (
            <button
              onClick={rightAction.onClick}
              aria-label={rightAction.label}
              className="h-12 min-w-[52px] flex items-center justify-center text-white hover:bg-gray-800 transition-none"
            >
              {rightAction.icon}
            </button>
          )}
        </div>
      </div>
    </header>
  )
}
