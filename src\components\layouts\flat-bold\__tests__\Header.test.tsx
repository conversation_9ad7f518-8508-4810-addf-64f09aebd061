import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { Header } from '../Header'

describe('Flat-Bold Header Component', () => {
  it('should render full-width color band', () => {
    render(<Header title="DR MUSCLE" />)

    const header = screen.getByRole('banner')
    expect(header).toHaveClass('w-full')
    expect(header).toHaveClass('bg-black')
    expect(header).toHaveClass('text-white')
  })

  it('should display bold title', () => {
    render(<Header title="WORKOUT" />)

    const title = screen.getByText('WORKOUT')
    expect(title).toHaveClass('font-heading')
    expect(title).toHaveClass('font-bold')
    expect(title).toHaveClass('text-2xl')
    expect(title).toHaveClass('uppercase')
  })

  it('should support left action button', () => {
    const handleBack = vi.fn()
    render(
      <Header
        title="EXERCISE"
        leftAction={{
          icon: <div>←</div>,
          onClick: handleBack,
        }}
      />
    )

    const backButton = screen.getByText('←').closest('button')
    expect(backButton).toBeInTheDocument()

    fireEvent.click(backButton!)
    expect(handleBack).toHaveBeenCalled()
  })

  it('should support right action button', () => {
    const handleMenu = vi.fn()
    render(
      <Header
        title="SETTINGS"
        rightAction={{
          icon: <div>☰</div>,
          onClick: handleMenu,
        }}
      />
    )

    const menuButton = screen.getByText('☰').closest('button')
    expect(menuButton).toBeInTheDocument()

    fireEvent.click(menuButton!)
    expect(handleMenu).toHaveBeenCalled()
  })

  it('should have fixed height', () => {
    render(<Header title="TEST" />)

    const header = screen.getByRole('banner')
    expect(header).toHaveClass('h-16') // 64px
  })

  it('should center title when no actions', () => {
    render(<Header title="CENTERED" />)

    const title = screen.getByText('CENTERED')
    expect(title.parentElement).toHaveClass('justify-center')
  })

  it('should have sharp corners', () => {
    render(<Header title="SHARP" />)

    const header = screen.getByRole('banner')
    expect(header).not.toHaveClass('rounded')
  })

  it('should support subtitle', () => {
    render(<Header title="BENCH PRESS" subtitle="3 sets remaining" />)

    expect(screen.getByText('BENCH PRESS')).toBeInTheDocument()
    expect(screen.getByText('3 sets remaining')).toBeInTheDocument()

    const subtitle = screen.getByText('3 sets remaining')
    expect(subtitle).toHaveClass('text-[#00FF88]')
    expect(subtitle).toHaveClass('text-sm')
  })

  it('should have proper button touch targets', () => {
    render(
      <Header
        title="TEST"
        leftAction={{ icon: <div>←</div>, onClick: () => {} }}
        rightAction={{ icon: <div>☰</div>, onClick: () => {} }}
      />
    )

    const buttons = screen.getAllByRole('button')
    buttons.forEach((button) => {
      expect(button).toHaveClass('min-w-[52px]')
      expect(button).toHaveClass('h-12')
    })
  })
})
