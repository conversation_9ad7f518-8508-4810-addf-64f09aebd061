import { queryClient } from '@/utils/queryClient'

/**
 * Clear all cached data from the application
 * Called on logout to ensure clean state
 */
export async function clearAllCaches() {
  // Clear localStorage keys used by our stores
  const storesToClear = [
    'drmuscle-auth', // authStore
    'drmuscle-workout', // workoutStore
    'drmuscle-program', // programStore
    'drmuscle-cache', // cacheStore
    'drmuscle-failed-requests', // SyncManager failed requests
    'drmuscle-offline-queue', // SyncManager offline queue
  ]

  storesToClear.forEach((key) => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error(`Failed to clear ${key}:`, error)
    }
  })
}

/**
 * Clear auth-related caches during logout
 * Now also clears workout cache to prevent cross-user data conflicts
 */
export async function clearAuthCaches() {
  // Clear only auth-related localStorage keys
  const authStoresToClear = [
    'drmuscle-auth', // authStore - clear auth tokens
    'drmuscle-program', // programStore - user-specific program data
    'drmuscle-cache', // cacheStore - may contain user-specific data
    'drmuscle-failed-requests', // SyncManager failed requests
    'drmuscle-offline-queue', // SyncManager offline queue
    'drmuscle-workout', // IMPORTANT: Clear workout cache to prevent cross-user data conflicts
    'user-stats-storage', // IMPORTANT: Clear user stats to prevent stats loading twice after login
  ]

  authStoresToClear.forEach((key) => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error(`Failed to clear ${key}:`, error)
    }
  })

  // Clear service worker caches
  if ('caches' in window) {
    try {
      const cacheNames = await caches.keys()
      await Promise.all(cacheNames.map((cacheName) => caches.delete(cacheName)))
    } catch (error) {
      console.error('Failed to clear service worker caches:', error)
    }
  }

  // Clear IndexedDB (if we use it in the future)
  if ('indexedDB' in window) {
    try {
      const databases = (await indexedDB.databases?.()) || []
      await Promise.all(
        databases
          .filter((db) => db.name)
          .map((db) => indexedDB.deleteDatabase(db.name!))
      )
    } catch (error) {
      // IndexedDB.databases() might not be supported in all browsers
      console.warn('Failed to clear IndexedDB:', error)
    }
  }

  // Clear sessionStorage
  try {
    sessionStorage.clear()
  } catch (error) {
    console.error('Failed to clear sessionStorage:', error)
  }

  // Clear React Query cache to prevent cross-user data conflicts
  try {
    queryClient.clear()
    // Also cancel any ongoing queries to prevent them from repopulating the cache
    queryClient.cancelQueries()
  } catch (error) {
    console.error('Failed to clear React Query cache:', error)
  }
}
