import React from 'react'
import { ShareIcon } from '@/components/icons'

interface ShareButtonProps {
  shareData?: {
    title: string
    text: string
  }
  className?: string
  showLabel?: boolean
}

export function ShareButton({
  shareData,
  className,
  showLabel = false,
}: ShareButtonProps) {
  const handleShareClick = async () => {
    const dataToShare = shareData || {
      title: 'Workout Complete!',
      text: 'I just completed my workout with Dr. Muscle X!',
    }

    if (navigator.share) {
      try {
        await navigator.share(dataToShare)
      } catch (error) {
        // Share cancelled or failed - no action needed
      }
    } else {
      // Fallback: Copy to clipboard
      try {
        const shareText = `${dataToShare.title}\n\n${dataToShare.text}`
        await navigator.clipboard.writeText(shareText)

        // Show temporary success feedback (could be enhanced with toast)
        const button = document.activeElement as HTMLButtonElement
        if (button && showLabel) {
          const span = button.querySelector('span')
          if (span) {
            const originalText = span.textContent
            span.textContent = 'Copied!'
            setTimeout(() => {
              span.textContent = originalText
            }, 2000)
          }
        }
      } catch (error) {
        // Clipboard write failed - could show error toast
      }
    }
  }

  const defaultClasses =
    'p-3 -mr-2 rounded-lg hover:bg-gray-100 transition-colors min-h-[52px] min-w-[52px] flex items-center justify-center gap-2'
  const buttonClasses = className
    ? `${className} min-h-[52px] min-w-[52px] flex items-center justify-center gap-2`
    : defaultClasses

  return (
    <button
      onClick={handleShareClick}
      className={buttonClasses}
      aria-label="Share"
    >
      <ShareIcon size={24} className="text-text-primary" />
      {showLabel && <span>Share</span>}
    </button>
  )
}
