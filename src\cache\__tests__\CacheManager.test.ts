/**
 * Tests for CacheManager
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { CacheManager } from '../CacheManager'
import { MemoryAdapter } from '../adapters/MemoryAdapter'
import type { CacheAdapter, CacheEntry, CacheMetadata } from '../types'
import { CacheKeyError, CacheSerializationError, CACHE_ENTRY_VERSION } from '../types'

// Mock adapter for testing manager logic
class MockAdapter implements CacheAdapter {
  private storage = new Map<string, CacheEntry>()
  
  async get<T>(key: string): Promise<CacheEntry<T> | null> {
    return this.storage.get(key) as CacheEntry<T> || null
  }

  async set<T>(key: string, value: T, metadata: CacheMetadata): Promise<void> {
    this.storage.set(key, { value, metadata })
  }

  async delete(key: string): Promise<void> {
    this.storage.delete(key)
  }

  async clear(): Promise<void> {
    this.storage.clear()
  }

  async getAllKeys(): Promise<string[]> {
    return Array.from(this.storage.keys())
  }

  async getSize(): Promise<number> {
    return this.storage.size * 100 // Mock size
  }

  supportsBatch(): boolean {
    return false
  }
}

describe('CacheManager', () => {
  let manager: CacheManager
  let mockAdapter: MockAdapter

  beforeEach(() => {
    mockAdapter = new MockAdapter()
    manager = new CacheManager(mockAdapter, {
      enableStats: true,
      defaultNamespace: 'test',
    })
  })

  afterEach(() => {
    manager.destroy()
  })

  describe('Basic Operations', () => {
    it('should store and retrieve values', async () => {
      const key = 'test-key'
      const value = { data: 'test-value' }

      await manager.set(key, value)
      const result = await manager.get(key)

      expect(result).toEqual(value)
    })

    it('should return null for non-existent keys', async () => {
      const result = await manager.get('non-existent')
      expect(result).toBeNull()
    })

    it('should delete values', async () => {
      const key = 'test-key'
      const value = 'test-value'

      await manager.set(key, value)
      await manager.delete(key)
      
      const result = await manager.get(key)
      expect(result).toBeNull()
    })

    it('should clear all values', async () => {
      await manager.set('key1', 'value1')
      await manager.set('key2', 'value2')

      await manager.clear()

      expect(await manager.get('key1')).toBeNull()
      expect(await manager.get('key2')).toBeNull()
    })
  })

  describe('Namespace Support', () => {
    it('should isolate values by namespace', async () => {
      const key = 'shared-key'
      const value1 = 'value-in-ns1'
      const value2 = 'value-in-ns2'

      await manager.set(key, value1, { namespace: 'ns1' })
      await manager.set(key, value2, { namespace: 'ns2' })

      expect(await manager.get(key, 'ns1')).toBe(value1)
      expect(await manager.get(key, 'ns2')).toBe(value2)
    })

    it('should use default namespace when none specified', async () => {
      const key = 'test-key'
      const value = 'test-value'

      await manager.set(key, value)
      const result = await manager.get(key)

      expect(result).toBe(value)
    })

    it('should clear specific namespace', async () => {
      await manager.set('key1', 'value1', { namespace: 'ns1' })
      await manager.set('key2', 'value2', { namespace: 'ns2' })

      await manager.clear('ns1')

      expect(await manager.get('key1', 'ns1')).toBeNull()
      expect(await manager.get('key2', 'ns2')).toBe('value2')
    })

    it('should get keys for specific namespace', async () => {
      await manager.set('key1', 'value1', { namespace: 'ns1' })
      await manager.set('key2', 'value2', { namespace: 'ns1' })
      await manager.set('key3', 'value3', { namespace: 'ns2' })

      const keys = await manager.getAllKeys('ns1')

      expect(keys).toHaveLength(2)
      expect(keys).toContain('key1')
      expect(keys).toContain('key2')
      expect(keys).not.toContain('key3')
    })
  })

  describe('TTL Support', () => {
    it('should set TTL from options', async () => {
      const key = 'ttl-key'
      const value = 'ttl-value'
      const ttl = 1000 // 1 second

      await manager.set(key, value, { ttl })
      const metadata = await manager.getMetadata(key)

      expect(metadata).not.toBeNull()
      expect(metadata!.expires).toBeGreaterThan(Date.now())
      expect(metadata!.expires).toBeLessThanOrEqual(Date.now() + ttl)
    })

    it('should use default TTL when none specified', async () => {
      const key = 'default-ttl-key'
      const value = 'default-ttl-value'

      await manager.set(key, value)
      const metadata = await manager.getMetadata(key)

      expect(metadata).not.toBeNull()
      expect(metadata!.expires).toBeGreaterThan(Date.now())
    })
  })

  describe('Batch Operations', () => {
    it('should perform batch get operations', async () => {
      await manager.set('key1', 'value1')
      await manager.set('key2', 'value2')
      await manager.set('key3', 'value3')

      const results = await manager.getMany(['key1', 'key2', 'nonexistent'])

      expect(results.size).toBe(2)
      expect(results.get('key1')).toBe('value1')
      expect(results.get('key2')).toBe('value2')
      expect(results.has('nonexistent')).toBe(false)
    })

    it('should perform batch set operations', async () => {
      const entries = new Map([
        ['key1', 'value1'],
        ['key2', 'value2'],
      ])

      await manager.setMany(entries)

      expect(await manager.get('key1')).toBe('value1')
      expect(await manager.get('key2')).toBe('value2')
    })

    it('should perform batch delete operations', async () => {
      await manager.set('key1', 'value1')
      await manager.set('key2', 'value2')
      await manager.set('key3', 'value3')

      await manager.deleteMany(['key1', 'key2'])

      expect(await manager.get('key1')).toBeNull()
      expect(await manager.get('key2')).toBeNull()
      expect(await manager.get('key3')).toBe('value3')
    })
  })

  describe('Pattern Invalidation', () => {
    it('should invalidate keys matching string pattern', async () => {
      await manager.set('user_123', 'data1')
      await manager.set('user_456', 'data2')
      await manager.set('post_789', 'data3')

      await manager.invalidate('user_')

      expect(await manager.get('user_123')).toBeNull()
      expect(await manager.get('user_456')).toBeNull()
      expect(await manager.get('post_789')).toBe('data3')
    })

    it('should invalidate keys matching regex pattern', async () => {
      await manager.set('user-123', 'data1')
      await manager.set('user-456', 'data2')
      await manager.set('post-789', 'data3')

      await manager.invalidate(/^user-/)

      expect(await manager.get('user-123')).toBeNull()
      expect(await manager.get('user-456')).toBeNull()
      expect(await manager.get('post-789')).toBe('data3')
    })
  })

  describe('Key Validation', () => {
    it('should reject empty keys', async () => {
      await expect(manager.set('', 'value'))
        .rejects.toThrow('Failed to set cache entry for key ""')
    })

    it('should reject keys with colons', async () => {
      await expect(manager.set('key:with:colons', 'value'))
        .rejects.toThrow('Failed to set cache entry for key "key:with:colons"')
    })

    it('should reject non-string keys', async () => {
      await expect(manager.set(null as any, 'value'))
        .rejects.toThrow('Failed to set cache entry for key "null"')
    })
  })

  describe('Value Validation', () => {
    it('should reject undefined values', async () => {
      await expect(manager.set('key', undefined))
        .rejects.toThrow('Failed to set cache entry for key "key"')
    })

    it('should accept null values', async () => {
      await manager.set('key', null)
      const result = await manager.get('key')
      expect(result).toBeNull()
    })

    it('should accept various data types', async () => {
      const testCases = [
        ['string', 'test-string'],
        ['number', 42],
        ['boolean', true],
        ['array', [1, 2, 3]],
        ['object', { key: 'value' }],
      ]

      for (const [type, value] of testCases) {
        await manager.set(`test-${type}`, value)
        const result = await manager.get(`test-${type}`)
        expect(result).toEqual(value)
      }
    })
  })

  describe('Statistics', () => {
    it('should track cache hits and misses', async () => {
      await manager.set('key1', 'value1')

      // Hit
      await manager.get('key1')
      // Miss
      await manager.get('nonexistent')

      const stats = await manager.getStats()

      expect(stats.hits).toBe(1)
      expect(stats.misses).toBe(1)
      expect(stats.hitRatio).toBe(0.5)
    })

    it('should track namespace statistics', async () => {
      await manager.set('key1', 'value1', { namespace: 'ns1' })
      await manager.set('key2', 'value2', { namespace: 'ns2' })

      await manager.get('key1', 'ns1') // Hit in ns1
      await manager.get('nonexistent', 'ns2') // Miss in ns2

      const stats = await manager.getStats()

      expect(stats.namespaces.ns1.hits).toBe(1)
      expect(stats.namespaces.ns1.misses).toBe(0)
      expect(stats.namespaces.ns2.hits).toBe(0)
      expect(stats.namespaces.ns2.misses).toBe(1)
    })
  })

  describe('Metadata Operations', () => {
    it('should retrieve entry metadata', async () => {
      const key = 'metadata-key'
      const value = 'metadata-value'
      const namespace = 'metadata-ns'

      await manager.set(key, value, { namespace, ttl: 5000 })
      const metadata = await manager.getMetadata(key, namespace)

      expect(metadata).not.toBeNull()
      expect(metadata!.namespace).toBe(namespace)
      expect(metadata!.version).toBe(CACHE_ENTRY_VERSION)
      expect(metadata!.created).toBeGreaterThan(0)
      expect(metadata!.expires).toBeGreaterThan(Date.now())
    })

    it('should return null metadata for non-existent keys', async () => {
      const metadata = await manager.getMetadata('nonexistent')
      expect(metadata).toBeNull()
    })
  })

  describe('Size Operations', () => {
    it('should get total cache size', async () => {
      await manager.set('key1', 'value1')
      await manager.set('key2', 'value2')

      const size = await manager.getSize()
      expect(size).toBeGreaterThan(0)
    })
  })

  describe('Integration with Real Adapter', () => {
    it('should work with MemoryAdapter', async () => {
      const memoryAdapter = new MemoryAdapter({ enableAutoCleanup: false })
      const memoryManager = new CacheManager(memoryAdapter)

      try {
        await memoryManager.set('integration-key', 'integration-value')
        const result = await memoryManager.get('integration-key')

        expect(result).toBe('integration-value')
      } finally {
        memoryManager.destroy()
      }
    })
  })

  describe('Cleanup', () => {
    it('should start and stop cleanup process', () => {
      expect(() => manager.startCleanup()).not.toThrow()
      expect(() => manager.stopCleanup()).not.toThrow()
    })

    it('should handle cleanup errors gracefully', async () => {
      // Mock console.warn to verify error handling
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      // Create adapter that throws on cleanup
      const errorAdapter = {
        ...mockAdapter,
        cleanup: vi.fn().mockRejectedValue(new Error('Cleanup failed'))
      }

      const errorManager = new CacheManager(errorAdapter as any)

      await expect(errorManager.cleanup()).rejects.toThrow('Cleanup failed')

      errorManager.destroy()
      consoleSpy.mockRestore()
    })
  })
})
