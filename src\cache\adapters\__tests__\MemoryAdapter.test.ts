/**
 * Tests for MemoryAdapter
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { MemoryAdapter } from '../MemoryAdapter'
import type { CacheMetadata } from '../../types'
import { CACHE_ENTRY_VERSION } from '../../types'

describe('MemoryAdapter', () => {
  let adapter: MemoryAdapter
  let mockMetadata: CacheMetadata

  beforeEach(() => {
    adapter = new MemoryAdapter({ enableAutoCleanup: false })
    mockMetadata = {
      size: 100,
      created: Date.now(),
      accessed: Date.now(),
      expires: 0, // Never expires
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
    }
  })

  afterEach(() => {
    adapter.destroy()
  })

  describe('Basic CRUD Operations', () => {
    it('should store and retrieve values', async () => {
      const key = 'test-key'
      const value = { data: 'test-value' }

      await adapter.set(key, value, mockMetadata)
      const result = await adapter.get(key)

      expect(result).not.toBeNull()
      expect(result?.value).toEqual(value)
      expect(result?.metadata.namespace).toBe('test')
    })

    it('should return null for non-existent keys', async () => {
      const result = await adapter.get('non-existent')
      expect(result).toBeNull()
    })

    it('should delete values', async () => {
      const key = 'test-key'
      const value = 'test-value'

      await adapter.set(key, value, mockMetadata)
      await adapter.delete(key)
      
      const result = await adapter.get(key)
      expect(result).toBeNull()
    })

    it('should clear all values', async () => {
      await adapter.set('key1', 'value1', mockMetadata)
      await adapter.set('key2', 'value2', mockMetadata)

      await adapter.clear()

      expect(await adapter.get('key1')).toBeNull()
      expect(await adapter.get('key2')).toBeNull()
      expect(await adapter.getSize()).toBe(0)
    })
  })

  describe('TTL and Expiration', () => {
    it('should return null for expired entries', async () => {
      const key = 'expired-key'
      const value = 'test-value'
      const expiredMetadata = {
        ...mockMetadata,
        expires: Date.now() - 1000, // Expired 1 second ago
      }

      await adapter.set(key, value, expiredMetadata)
      const result = await adapter.get(key)

      expect(result).toBeNull()
    })

    it('should clean up expired entries on get', async () => {
      const key = 'expired-key'
      const value = 'test-value'
      const expiredMetadata = {
        ...mockMetadata,
        expires: Date.now() - 1000,
      }

      await adapter.set(key, value, expiredMetadata)
      await adapter.get(key) // This should trigger cleanup

      const keys = await adapter.getAllKeys()
      expect(keys).not.toContain(key)
    })

    it('should not expire entries with expires = 0', async () => {
      const key = 'never-expires'
      const value = 'test-value'
      const neverExpiresMetadata = {
        ...mockMetadata,
        expires: 0,
      }

      await adapter.set(key, value, neverExpiresMetadata)
      
      // Wait a bit to ensure it doesn't expire
      await new Promise(resolve => setTimeout(resolve, 10))
      
      const result = await adapter.get(key)
      expect(result).not.toBeNull()
      expect(result?.value).toBe(value)
    })
  })

  describe('Size Tracking', () => {
    it('should track total cache size', async () => {
      const initialSize = await adapter.getSize()
      expect(initialSize).toBe(0)

      await adapter.set('key1', 'value1', mockMetadata)
      const sizeAfterFirst = await adapter.getSize()
      expect(sizeAfterFirst).toBeGreaterThan(0)

      await adapter.set('key2', 'value2', mockMetadata)
      const sizeAfterSecond = await adapter.getSize()
      expect(sizeAfterSecond).toBeGreaterThan(sizeAfterFirst)
    })

    it('should update size when deleting entries', async () => {
      await adapter.set('key1', 'value1', mockMetadata)
      await adapter.set('key2', 'value2', mockMetadata)
      
      const sizeBeforeDelete = await adapter.getSize()
      await adapter.delete('key1')
      const sizeAfterDelete = await adapter.getSize()

      expect(sizeAfterDelete).toBeLessThan(sizeBeforeDelete)
    })

    it('should reset size when clearing cache', async () => {
      await adapter.set('key1', 'value1', mockMetadata)
      await adapter.set('key2', 'value2', mockMetadata)

      await adapter.clear()
      const size = await adapter.getSize()

      expect(size).toBe(0)
    })
  })

  describe('Batch Operations', () => {
    it('should support batch operations', () => {
      expect(adapter.supportsBatch()).toBe(true)
    })

    it('should perform batch get operations', async () => {
      await adapter.set('key1', 'value1', mockMetadata)
      await adapter.set('key2', 'value2', mockMetadata)
      await adapter.set('key3', 'value3', mockMetadata)

      const results = await adapter.getMany(['key1', 'key2', 'nonexistent'])

      expect(results.size).toBe(2)
      expect(results.get('key1')?.value).toBe('value1')
      expect(results.get('key2')?.value).toBe('value2')
      expect(results.has('nonexistent')).toBe(false)
    })

    it('should perform batch set operations', async () => {
      const entries = new Map([
        ['key1', { value: 'value1', metadata: mockMetadata }],
        ['key2', { value: 'value2', metadata: mockMetadata }],
      ])

      await adapter.setMany(entries)

      expect(await adapter.get('key1')).not.toBeNull()
      expect(await adapter.get('key2')).not.toBeNull()
    })

    it('should perform batch delete operations', async () => {
      await adapter.set('key1', 'value1', mockMetadata)
      await adapter.set('key2', 'value2', mockMetadata)
      await adapter.set('key3', 'value3', mockMetadata)

      await adapter.deleteMany(['key1', 'key2'])

      expect(await adapter.get('key1')).toBeNull()
      expect(await adapter.get('key2')).toBeNull()
      expect(await adapter.get('key3')).not.toBeNull()
    })
  })

  describe('Key Management', () => {
    it('should return all valid keys', async () => {
      await adapter.set('key1', 'value1', mockMetadata)
      await adapter.set('key2', 'value2', mockMetadata)

      const keys = await adapter.getAllKeys()

      expect(keys).toHaveLength(2)
      expect(keys).toContain('key1')
      expect(keys).toContain('key2')
    })

    it('should exclude expired keys from getAllKeys', async () => {
      const expiredMetadata = {
        ...mockMetadata,
        expires: Date.now() - 1000,
      }

      await adapter.set('valid-key', 'value1', mockMetadata)
      await adapter.set('expired-key', 'value2', expiredMetadata)

      const keys = await adapter.getAllKeys()

      expect(keys).toContain('valid-key')
      expect(keys).not.toContain('expired-key')
    })
  })

  describe('LRU Eviction', () => {
    it('should evict LRU entries when max entries exceeded', async () => {
      const smallAdapter = new MemoryAdapter({ 
        maxEntries: 2,
        enableAutoCleanup: false 
      })

      try {
        // Fill to capacity
        await smallAdapter.set('key1', 'value1', { ...mockMetadata, accessed: 1000 })
        await smallAdapter.set('key2', 'value2', { ...mockMetadata, accessed: 2000 })

        // This should evict key1 (oldest access time)
        await smallAdapter.set('key3', 'value3', { ...mockMetadata, accessed: 3000 })

        expect(await smallAdapter.get('key1')).toBeNull()
        expect(await smallAdapter.get('key2')).not.toBeNull()
        expect(await smallAdapter.get('key3')).not.toBeNull()
      } finally {
        smallAdapter.destroy()
      }
    })
  })

  describe('Cleanup', () => {
    it('should manually clean up expired entries', async () => {
      const expiredMetadata = {
        ...mockMetadata,
        expires: Date.now() - 1000,
      }

      await adapter.set('valid-key', 'value1', mockMetadata)
      await adapter.set('expired-key1', 'value2', expiredMetadata)
      await adapter.set('expired-key2', 'value3', expiredMetadata)

      const cleanedCount = await adapter.cleanup()

      expect(cleanedCount).toBe(2)
      expect(await adapter.get('valid-key')).not.toBeNull()
      expect(await adapter.get('expired-key1')).toBeNull()
      expect(await adapter.get('expired-key2')).toBeNull()
    })
  })

  describe('Error Handling', () => {
    it('should handle serialization errors gracefully', async () => {
      const circularObj: any = {}
      circularObj.self = circularObj

      await expect(adapter.set('circular', circularObj, mockMetadata))
        .rejects.toThrow('Failed to set cache entry')
    })
  })

  describe('Access Time Updates', () => {
    it('should update access time on get', async () => {
      const key = 'test-key'
      const value = 'test-value'
      const originalTime = Date.now() - 1000

      await adapter.set(key, value, { ...mockMetadata, accessed: originalTime })

      // Wait a bit to ensure time difference
      await new Promise(resolve => setTimeout(resolve, 10))

      const result = await adapter.get(key)
      expect(result?.metadata.accessed).toBeGreaterThan(originalTime)
    })
  })

  describe('Statistics', () => {
    it('should provide cache statistics', async () => {
      await adapter.set('key1', 'value1', mockMetadata)
      await adapter.set('key2', 'value2', mockMetadata)

      const stats = adapter.getStats()

      expect(stats.entryCount).toBe(2)
      expect(stats.totalSize).toBeGreaterThan(0)
      expect(stats.maxEntries).toBe(1000) // Default max entries
    })
  })
})
