import React from 'react'
import { cn } from '@/lib/utils'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  children: React.ReactNode
}

export function Button({
  variant = 'primary',
  size = 'md',
  children,
  disabled,
  className,
  ...props
}: ButtonProps) {
  const baseClasses =
    'font-bold border-2 rounded-none transition-none min-w-[52px] px-6 uppercase tracking-wide'

  const variantClasses = {
    primary:
      'bg-[#00FF88] text-black border-black hover:bg-[#00CC6A] active:bg-black active:text-[#00FF88]',
    secondary:
      'bg-white text-black border-black hover:bg-gray-100 active:bg-black active:text-white',
    danger:
      'bg-[#FF0044] text-white border-black hover:bg-[#CC0033] active:bg-black active:text-[#FF0044]',
  }

  const sizeClasses = {
    sm: 'h-12 text-sm', // 48px
    md: 'h-14 text-base', // 56px
    lg: 'h-16 text-lg', // 64px
  }

  const disabledClasses = disabled
    ? 'bg-gray-400 text-gray-600 border-gray-600 cursor-not-allowed hover:bg-gray-400 active:bg-gray-400 active:text-gray-600'
    : ''

  return (
    <button
      className={cn(
        baseClasses,
        !disabled && variantClasses[variant],
        sizeClasses[size],
        disabledClasses,
        className
      )}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  )
}
