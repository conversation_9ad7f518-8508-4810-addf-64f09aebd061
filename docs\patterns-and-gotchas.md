# Development Patterns & Gotchas

This document captures important patterns, common issues, and their solutions based on our development experience. Always consult this before implementing new features.

## Common Issues & Solutions

### Navigation & Routing

#### Invalid Exercise ID Navigation

**Issue**: Users see "Exercise Not Available" error when accessing invalid exercise URLs (e.g., `/workout/exercise/0`, `/workout/exercise/abc`).
**Root Cause**: Dynamic route accepts any string ID, but `parseInt()` can return `NaN` or invalid numbers.
**Solution**: Add server-side validation at the route level before rendering client component:

```typescript
// In src/app/workout/exercise/[id]/page.tsx
export default async function ExercisePage({ params }: ExercisePageProps) {
  const { id } = await params
  const exerciseId = parseInt(id)

  // Validate exercise ID at the route level
  if (!isValidExerciseId(exerciseId)) {
    redirect('/workout')
  }

  return <ExercisePageClient exerciseId={exerciseId} />
}
```

**Key Points**:

- Use `isValidExerciseId()` utility to check for positive integers
- Server-side redirect prevents error boundary from triggering
- Maintains existing validation for exercises not in current workout

### Race Conditions

#### Workout Start Race Condition

**Issue**: Users see blank screen when starting workout due to exercises not loading before navigation.
**Root Cause**: React closure captures stale `exercises` array, navigation happens before `startWorkout` completes.
**Solution**: Return `firstExerciseId` from `startWorkout` function and use fresh data:

```typescript
// BAD: Uses potentially stale exercises array
const firstExercise = exercises[0]
navigate(`/workout/exercise/${firstExercise.Id}`)

// GOOD: Uses fresh data from store action
const { firstExerciseId } = await startWorkout()
navigate(`/workout/exercise/${firstExerciseId}`)
```

#### Exercise Recommendations Loading

**Issue**: Exercise page shows no recommendations when navigating too quickly.
**Pattern**: Always wait for async operations to complete before navigation.

```typescript
// Ensure recommendations are loaded
await loadRecommendation(exerciseId)
// Then navigate
```

### Component State Management

#### Active Set Display Updates

**Issue**: Arrow buttons don't update displayed values for active set.
**Root Cause**: `generateAllSets()` wasn't using current `setData` for active set.
**Solution**: Incorporate `setData` values when generating sets:

```typescript
// For active set, use current setData values
if (isActiveSet) {
  set.Reps = setData.reps
  set.Weight = convertWeight(setData.weight, userUnit)
}
```

#### Navigation Title Persistence

**Issue**: Exercise name persists in navigation header when leaving exercise page.
**Solution**: Clear dynamic title when unmounting:

```typescript
useEffect(() => {
  return () => setTitle(null) // Clear on unmount
}, [])
```

### Testing Patterns

#### WebKit/Safari Stability

**Issue**: Tests fail intermittently on WebKit due to timing issues.
**Solutions**:

1. Add explicit waits for UI elements: `await page.waitForSelector('.save-button', { state: 'visible' })`
2. Use more specific selectors: `button:has-text("Save set")` instead of generic classes
3. Add retry logic for flaky operations
4. Check element visibility before interaction

#### Test Data Isolation

**Pattern**: Always use unique test data to avoid conflicts:

```typescript
const uniqueEmail = `test-${Date.now()}@example.com`
```

#### Mocking API Responses

**Pattern**: Mock at the network level for E2E tests, at the module level for unit tests:

```typescript
// E2E: Use Playwright's route handler
await page.route('**/api/exercise/*', (route) =>
  route.fulfill({ body: mockData })
)

// Unit: Use vitest mocks
vi.mock('@/services/api', () => ({ getExercise: vi.fn() }))
```

## UI/UX Patterns

### Touch Targets

**Requirement**: Minimum 52px touch targets for mobile.
**Implementation**: Use padding, not just size:

```css
/* BAD: Only 32px clickable area */
.button {
  width: 32px;
  height: 32px;
}

/* GOOD: 52px clickable area with padding */
.button {
  width: 32px;
  height: 32px;
  padding: 10px; /* Total 52px */
}
```

### Grid Layout for Sets

**Pattern**: Display all sets at once in a grid format:

- Headers: SET | REPS | \* | LBS/KG
- Show 0-6 warmup sets and 2-6 work sets based on API
- Active set shows arrow buttons for adjustment
- Completed sets show check mark, skipped show X

### Save Button Behavior

**Pattern**: Consolidate save functionality within the grid:

- Save button appears under active set only
- Shows loading state during save
- Disable during save to prevent double-submission

## State Management Patterns

### Zustand Store Best Practices

**Pattern**: Return fresh data from actions to avoid closure issues:

```typescript
// Store action
startWorkout: async () => {
  const workout = await api.startWorkout()
  set({ workout, exercises: workout.exercises })
  return { firstExerciseId: workout.exercises[0].Id }
}
```

### Component Splitting for Maintainability

**Rule**: Max 200 lines per component. Split large components:

- Extract state logic into custom hooks
- Extract sub-components for distinct UI sections
- Extract action handlers into separate modules

Example: `WorkoutOverview.tsx` split into:

- `WorkoutOverview.tsx` (main component)
- `WorkoutOverviewStates.tsx` (state sections)
- `WorkoutOverviewActions.tsx` (action buttons)

## API Integration Patterns

### Error Handling

**Pattern**: Handle expected errors gracefully:

```typescript
// Reduce console noise for expected 404s
if (error.response?.status === 404) {
  console.info('No sets found for exercise')
  return { sets: [] }
}
```

### Unit Conversion

**Always**: Convert weights based on user preference:

```typescript
const displayWeight = convertWeight(
  weight,
  userInfo?.MassUnit === 'kg' ? 'kg' : 'lbs'
)
```

### Type Safety

**Rule**: Never use `any` types. Common type issues:

- Exercise IDs are numbers, not strings
- Recommendation can be null, handle accordingly
- Weight can be 0, don't prevent this

## Performance Patterns

### Bundle Size

**Constraint**: < 150KB initial JavaScript
**Check**: `npm run analyze` before committing
**Common issues**:

- Importing entire libraries instead of specific functions
- Not using dynamic imports for large components

### Mobile-First Development

**Always test on mobile viewport (320-430px)**:

- Use mobile gestures (swipe navigation)
- Optimize for mobile bandwidth
- Test touch interactions
- Verify performance on mid-range devices

## Modal Patterns

### Consistent Modal Implementation

**Pattern**: Use consistent modal pattern for UI coherence across the app:

```tsx
// Backdrop
<div
  className="fixed inset-0 bg-black/60 z-[70] animate-in fade-in duration-200"
  onClick={onClose}
  aria-hidden="true"
/>

// Modal
<div
  role="dialog"
  aria-modal="true"
  aria-labelledby="modal-title"
  aria-describedby="modal-description"
  className="fixed inset-x-4 top-[20vh] max-w-md mx-auto bg-surface-primary/95 rounded-3xl shadow-2xl z-[80] animate-in zoom-in-95 slide-in-from-bottom-2 duration-300 border border-surface-tertiary"
>
```

**Key Features**:

- **Backdrop**: Use `bg-black/60` for semi-transparent dark background
- **Modal**: Use `bg-surface-primary/95` for subtle transparency
- **Border**: Add `border border-surface-tertiary` for better definition
- **Rounded corners**: Use `rounded-3xl` for modern appearance
- **Z-index**: Use `z-[70]` for backdrop and `z-[80]` for modal
- **Animation**: Include smooth entry animations

**Required Functionality**:

- Escape key to close
- Click outside to close
- Body scroll lock when open
- Proper ARIA attributes for accessibility
- Focus management

**Reference Implementation**: See `CustomDurationModal` and `SetTypeExplainerModal`

## Don't Repeat These Mistakes

1. **Never skip tests**: Run `npm run typecheck`, `npm run lint`, `npm run test` before every commit
2. **Never use --no-verify**: Fix the issues, don't skip checks
3. **Never assume library availability**: Check package.json first
4. **Never update git config**: Use existing settings
5. **Never create files unless necessary**: Prefer editing existing files
6. **Never ignore TypeScript errors**: Fix them properly
7. **Never forget mobile testing**: All UI changes must be tested on mobile viewport
8. **Never commit without testing arrow functionality**: If touching sets UI, test increment/decrement
9. **Never navigate without ensuring data is loaded**: Prevent race conditions
10. **Never ignore user preferences**: Always respect kg/lbs settings
11. **Never trust route parameters**: Always validate dynamic route IDs at the server level
12. **Never change CI runner types without infrastructure verification**: Document and maintain runner consistency
13. **Never use mismatched Playwright package versions**: Keep `playwright` and `@playwright/test` synchronized
14. **Never use timeout command wrappers with Playwright**: Causes `exe.match is not a function` errors
15. **Never assume Linux commands work on macOS runners**: Use OS-conditional commands in CI workflows
16. **Never set timeout-minutes at workflow level**: Only job-level timeouts are valid in GitHub Actions
17. **Never create cross-runner job dependencies without testing**: Can cause startup failures and bottlenecks
18. **Never move WebKit/Safari tests to Linux runners**: macOS-specific testing must stay on macOS
19. **Never assume runner availability**: Test with minimal workflow before implementing complex dependencies
20. **Never allocate more memory than runner capacity**: 12GB allocation on 8GB runners causes OOM failures
21. **Never upload artifacts on success for storage optimization**: Use `if: failure()` to save 80% storage quota
22. **Never run E2E tests on every branch**: Restrict to main/staging branches for 70% CI time savings
23. **Never use `npm run test:coverage` for unit tests**: Runs ALL tests including E2E, use vitest directly
24. **Never forget to mock all Next.js navigation exports**: Missing useSearchParams, redirect causes test failures
25. **Never use manual server start/stop with Playwright**: Let Playwright's webServer handle server lifecycle
26. **Never disable WebKit without considering test coverage**: Chrome-only testing saves memory but reduces coverage
27. **Never ignore port conflicts in E2E tests**: Set `reuseExistingServer: true` in Playwright config
28. **Never create modals without following the consistent modal pattern**: Use consistent modal styling as documented above

## Quick Reference Commands

```bash
# Before ANY commit
npm run typecheck
npm run lint
npm run test
npm run build

# Check bundle size
npm run analyze

# Test specific areas
npm run test:unit
npm run test:components
npm run test:changed

# CI/E2E testing
npm run test:critical-flows
npm run test:e2e
npx playwright --version

# Playwright browser management
npx playwright install
npx playwright install --with-deps webkit chromium
npx playwright uninstall --all

# CI Optimization Commands
# Memory-optimized unit tests (for 8GB runners)
export NODE_OPTIONS="--max_old_space_size=4096 --max-semi-space-size=256"
npx vitest run --coverage --config vitest.config.parallel.mjs

# Memory-optimized E2E tests
export NODE_OPTIONS="--max_old_space_size=6144 --max-semi-space-size=512"
npx playwright test --config=playwright.ci.optimized.config.ts

# Check memory usage during CI
if [[ "$RUNNER_OS" == "macOS" ]]; then
  vm_stat | head -10
else
  free -h
fi

# Clean up CI artifacts and cache
rm -rf .next/ playwright-report/ test-results/ coverage/
npm ci --cache .npm --prefer-offline

# Test only changed files (fast feedback)
npm run test:changed -- --reporter=verbose

# Force clean install (CI troubleshooting)
rm -rf node_modules package-lock.json
npm install
```

### Set ID Generation and React Key Conflicts

**Issue**: React key conflicts and console warnings when rendering sets due to duplicate IDs.
**Root Cause**: Warmup and work sets used overlapping ID ranges, causing React to lose track of components.
**Solution**: Use distinct negative ID ranges for different set types:

```typescript
// Generate unique IDs to prevent React key conflicts
const warmupSets = sets.warmup.map((set, index) => ({
  ...set,
  Id: -(1000 + index), // Warmup sets: -1000, -1001, etc.
}))

const workSets = sets.work.map((set, index) => ({
  ...set,
  Id: -(2000 + index), // Work sets: -2000, -2001, etc.
}))
```

**Key Points**:

- Always use unique negative IDs for client-generated sets
- Reserve distinct ID ranges for different set types
- This prevents React key warnings and component state confusion

### Set Type Badge Display

**Issue**: Individual set type badges not showing for specialized sets (Rest-pause, Pyramid, Drop, etc.).
**Root Cause**: Set type detection relied only on exercise-level properties, not individual set flags.
**Solution**: Check both SetTitle and individual set flags:

```typescript
export function getSetTypeFromSet(set: any): SetType {
  // Check individual set flags first
  if (set.IsRestPause) return 'Rest-pause'
  if (set.IsPyramid) return 'Pyramid'
  if (set.IsDrop) return 'Drop'
  if (set.IsBackOff) return 'Back-off'
  if (set.IsReversePyramid) return 'Reverse pyramid'

  // Fallback to SetTitle for other types
  return getSetTypeFromTitle(set.SetTitle)
}
```

### Arrow Functionality Weight Decrements

**Issue**: Weight decrement arrows not working properly - reducing by 1 instead of proper increments.
**Root Cause**: Decrement logic wasn't using recommendation-based increments.
**Solution**: Apply same increment logic for both directions:

```typescript
const handleWeightDecrement = () => {
  const currentWeight = parseFloat(setData.weight) || 0
  const increment = recommendation?.WeightIncrement || 5
  const newWeight = Math.max(0, currentWeight - increment)
  updateSetData({ weight: newWeight.toString() })
}
```

**Key Points**:

- Always use `recommendation.WeightIncrement` for both increment and decrement
- Ensure minimum weight of 0 (never negative)
- Convert to string for form input compatibility

### Set Numbering Display

**Issue**: Confusing set numbering where warmup and work sets had overlapping numbers.
**Solution**: Clear visual distinction in numbering:

```typescript
// Display warmup sets as 'W' and work sets starting from 1
const getSetDisplayNumber = (set: any, setIndex: number, isWarmup: boolean) => {
  return isWarmup ? 'W' : (setIndex + 1).toString()
}
```

### Rest-Pause Sets Calculation

**Issue**: Rest-pause sets were completing exercise prematurely - exercise marked complete after first set.
**Root Cause**: Total sets calculation didn't include mini-sets from `NbPauses`.
**Solution**: Include rest-pause mini-sets in total sets calculation:

```typescript
// Calculate total sets including rest-pause mini-sets
const totalSets =
  workSets.length +
  workSets.reduce((acc, set) => {
    return acc + (set.NbPauses || 0)
  }, 0)

// Check if last set including mini-sets
const isLastSet = completedCount >= totalSets - 1
```

**Key Points**:

- Each rest-pause set generates additional mini-sets based on `NbPauses`
- Must count all mini-sets to prevent premature exercise completion
- First work set is normal, subsequent sets are rest-pause mini-sets

### Warmup Set Data Model and Display

**Issue**: Warmup sets were displaying identical values to work sets instead of proper warmup calculations.
**Root Cause**: `generateAllSets` was incorrectly setting both `Reps` and `WarmUpReps` properties on warmup sets. The `Reps` property should NOT be set for warmup sets - it should only have `WarmUpReps`.
**Solution**: Properly separate warmup and work set data properties:

```typescript
// Warmup sets use ONLY warmup-specific properties
const warmupSet = {
  WarmUpReps: number, // Calculated warmup reps (~75% of work reps)
  WarmUpWeightSet: {
    // Calculated warmup weights (~50% of work weight)
    Kg: number,
    Lb: number,
  },
  Weight: { Kg: 0, Lb: 0 }, // Always zero for warmups
  IsWarmups: true,
  // DO NOT SET: Reps property - this causes UI to read wrong values
}

// Work sets use standard properties
const workSet = {
  Reps: number, // Target reps
  Weight: {
    // Working weight
    Kg: number,
    Lb: number,
  },
  IsWarmups: false,
}
```

**Key Points**:

- **Critical**: Never set `Reps` property on warmup sets - only `WarmUpReps`
- Never copy `Weight` values for warmup sets - always use `WarmUpWeightSet`
- `createWorkoutSetsMAUI` must set `WarmUpReps` and `WarmUpWeightSet` for warmups
- UI reads from `WarmUpReps`/`WarmUpWeightSet` for warmups, `Reps`/`Weight` for work sets
- Warmup calculation algorithm: Weight ~50% of work weight, Reps ~75% of work reps
- Equipment-specific rounding (plates, dumbbells, bands)
- Special handling for bodyweight and assisted exercises

### Set Navigation Data Updates

**Issue**: Set data not updating when navigating between warmup and work sets.
**Root Cause**: Missing `useEffect` dependency on `currentSetIndex` in `useSetScreenLogic`.
**Solution**: Watch index changes to reload set data:

```typescript
// Add currentSetIndex to useEffect dependencies
useEffect(() => {
  if (exerciseId && currentSetIndex >= 0) {
    loadSetData(exerciseId, currentSetIndex)
  }
}, [exerciseId, currentSetIndex]) // Include currentSetIndex
```

**Prevention**: Always include state variables that trigger data reloading in `useEffect` dependencies.

### Warm-Up Set Editability After Saving

**Issue**: Warm-up sets lose editability after saving, becoming read-only and preventing further modifications.
**Root Cause**: `generateAllSets` was incorrectly setting both `Reps` and `WarmUpReps` properties on warmup sets, causing data model confusion that broke edit state persistence.
**Solution**: Strict separation of warmup and work set data properties:

```typescript
// CRITICAL: Warmup sets must ONLY use warmup-specific properties
const warmupSet = {
  WarmUpReps: calculatedWarmupReps,
  WarmUpWeightSet: { Kg: warmupKg, Lb: warmupLb },
  Weight: { Kg: 0, Lb: 0 }, // Always zero for warmups
  IsWarmups: true,
  // NEVER SET: Reps property - this breaks editability
}

// Work sets use standard properties
const workSet = {
  Reps: targetReps,
  Weight: { Kg: workKg, Lb: workLb },
  IsWarmups: false,
  // NEVER SET: WarmUpReps on work sets
}
```

**Key Points**:

- **NEVER** set `Reps` property on warmup sets - only `WarmUpReps`
- **NEVER** copy `Weight` values for warmup sets - always use `WarmUpWeightSet`
- Edit state persistence depends on proper data model separation
- This fix resolves commits `ee78d35` and `2f131ed`

**Prevention**: Always validate that data generation functions maintain strict property separation between warmup and work sets.

## Recent Architectural Decisions

1. **Grid Layout for Sets**: Replaced list view with grid showing all sets at once
2. **Inline Editing**: Direct editing in grid cells instead of modal
3. **Arrow Controls**: Quick increment/decrement for active set only
4. **Consolidated Save**: Single save button under active set
5. **Unique Set IDs**: Use distinct negative ranges to prevent React key conflicts
6. **Individual Set Type Badges**: Check individual set flags for specialized set types
7. **Status File Gitignored**: Use this patterns file and git history for context instead
8. **Rest-Pause Sets Logic**: Count all mini-sets to prevent premature completion
9. **Warmup Data Model Separation**: Use `WarmUpWeightSet` for display, `Weight: {0,0}` for warmups
10. **Set Navigation State Management**: Include `currentSetIndex` in effect dependencies
11. **Component Hierarchy Simplification**: Removed legacy `SetScreen` and `SetListMobile` - now single path through `ExerciseSetsGrid`

### Exercise Page Component Hierarchy

**Current Implementation**: The exercise page exclusively uses:

```
ExercisePageClient → SetScreenWithGrid → ExerciseSetsGrid
```

**Legacy Components Removed**:

- `SetScreen` component - completely removed
- `SetListMobile` component - completely removed
- All references to old components have been cleaned up

**Key Points**:

- The grid layout is the only implementation for displaying sets
- No conditional rendering between different set display components
- All exercise set functionality goes through `ExerciseSetsGrid`

## Wheel Picker Implementation Analysis

### Wheel Picker Attempt and Failure (PR #334)

**Issue**: WheelPicker component was implemented but failed in production due to interaction conflicts and poor user experience.

**Root Cause**: Multiple interaction conflicts between wheel picker drag gestures and existing page/swipe functionality:

1. **Scroll Conflicts**: Horizontal wheel picker drag interfered with vertical page scrolling
2. **Swipe Gesture Conflicts**: Wheel picker drag conflicted with set completion swipe gestures
3. **Touch Sensitivity Issues**: Rapid touch gestures caused flaky behavior and responsiveness problems
4. **Bundle Size Impact**: Added ~5KB to bundle size without proportional UX benefit

**Solution**: Replaced WheelPicker with proven arrow controls pattern in commit `1caddd7`:

```typescript
// FAILED: WheelPicker with drag conflicts
<WheelPicker
  value={reps}
  onChange={setReps}
  increment={1}
  min={1}
  max={50}
/>

// SUCCESS: Arrow controls with clear interaction model
<RepsInput
  value={reps}
  onChange={setReps}
  onIncrement={() => setReps(reps + 1)}
  onDecrement={() => setReps(Math.max(1, reps - 1))}
/>
```

**Key Learning**: The wheel picker interaction model, while visually appealing, created too many gesture conflicts in a mobile-first PWA with existing swipe and scroll interactions.

**Evidence**:

- Commit `c707aee`: Initial wheel picker implementation
- Commit `77a4d25`: Attempted sensitivity fixes
- Commit `1caddd7`: Complete removal and replacement with arrows
- Test file: `tests/e2e/wheel-picker-scroll.spec.ts` documents specific conflicts

**Prevention**:

- Always consider existing gesture interactions before adding new drag/swipe components
- Prioritize proven mobile interaction patterns over novel UI elements
- Test touch interactions thoroughly across different devices and scenarios
- Evaluate bundle size impact vs UX benefit for new components

**Alternative Considered**: The wheel picker pattern works well in iOS native apps but doesn't translate effectively to web PWAs with existing gesture systems.

## CI/CD Pipeline Patterns & Gotchas

### Self-Hosted macOS Runner Configuration

**Issue**: CI pipeline broken when switched from `[self-hosted, macos]` to `ubuntu-latest` runners.
**Root Cause**: The project was designed for Ubicloud and external Mac runners, but was accidentally changed to Ubuntu in commit `cd028cc`.
**Solution**: Always maintain runner consistency with your infrastructure:

```yaml
# CORRECT: For Ubicloud/external Mac setup
runs-on: [self-hosted, macos]

# WRONG: Breaks compatibility with existing setup
runs-on: ubuntu-latest
```

**Key Points**:

- Document your runner infrastructure in CI configuration comments
- Never change runner types without verifying infrastructure compatibility
- Self-hosted runners require different system commands than GitHub-hosted

### Playwright Installation on macOS

**Issue**: `exe.match is not a function` error during Playwright browser installation.
**Root Causes**:

1. Version mismatch between `playwright` and `@playwright/test` packages
2. Using `timeout` command wrapper that interferes with Playwright's internal execution

**Solution**: Ensure version alignment and avoid problematic command wrappers:

```json
// package.json - Keep versions in sync
{
  "@playwright/test": "^1.54.1",
  "playwright": "^1.54.1" // Must match @playwright/test
}
```

```yaml
# setup-playwright action - Remove timeout wrapper
# WRONG: timeout command causes exe.match errors
if timeout 600 npx playwright install --with-deps webkit chromium; then

# CORRECT: Direct execution without timeout wrapper
if npx playwright install --with-deps webkit chromium; then
```

**Prevention**:

- Always keep Playwright packages at the same version
- Avoid command wrappers that can interfere with Node.js execution
- Test CI changes on actual target infrastructure before merging

### macOS vs Linux Command Compatibility

**Issue**: Linux-specific commands fail on macOS runners.
**Solution**: Use OS-conditional commands in CI workflows:

```yaml
# Memory reporting - OS-specific commands
echo "Memory usage:"
if [[ "$RUNNER_OS" == "macOS" ]]; then
  vm_stat | head -10
else
  free -h
fi

# CPU usage - Different top command syntax
echo "CPU usage:"
if [[ "$RUNNER_OS" == "macOS" ]]; then
  top -l 1 -n 20
else
  top -bn1 | head -20
fi
```

**Common macOS vs Linux Differences**:

- Memory: `vm_stat` vs `free -h`
- CPU: `top -l 1` vs `top -bn1`
- Package management: `brew` vs `apt-get`
- Process management: Different `pkill` behavior

### Ubicloud Runner Integration

**Issue**: Startup failures when integrating ubicloud-standard-2 runners for load balancing.
**Root Causes**:

1. **Invalid Global Timeout**: `timeout-minutes: 90` at workflow level is invalid syntax
2. **Cross-Runner Dependencies**: Jobs on different runner types waiting for each other
3. **macOS-Specific Steps on Linux**: WebKit/Safari testing requires macOS

**Solution**: Proper runner distribution and dependency management:

```yaml
# WRONG: Global timeout causes startup failure
name: CI Pipeline
timeout-minutes: 90 # Invalid - not supported at workflow level

# CORRECT: Job-level timeouts only
jobs:
  test-unit:
    runs-on: ubicloud-standard-2
    timeout-minutes: 20 # Valid at job level
```

**Load Balancing Strategy**:

```yaml
# Platform-independent jobs → ubicloud-standard-2 (Linux)
test-unit:
  runs-on: ubicloud-standard-2
  timeout-minutes: 20

quality-gates:
  runs-on: ubicloud-standard-2
  timeout-minutes: 15

# macOS-specific jobs → [self-hosted, macos]
e2e-critical:
  runs-on: [self-hosted, macos] # WebKit/Safari requires macOS
  timeout-minutes: 30

build:
  runs-on: [self-hosted, macos] # Keep build consistency
  timeout-minutes: 25
```

**Key Points**:

- **Never** set `timeout-minutes` at workflow level - only at job level
- **Never** create dependencies between jobs on different runner types without careful consideration
- **Always** keep WebKit/Safari testing on macOS runners
- **Test** ubicloud availability with a simple test workflow first
- **Parallel execution** is better than sequential cross-runner dependencies

### GitHub Actions Workflow Syntax Gotchas

**Issue**: Workflow startup failures due to invalid YAML syntax.
**Common Mistakes**:

1. **Global timeouts**: Not supported at workflow level
2. **Invalid runner labels**: Must match available infrastructure
3. **Cross-runner job dependencies**: Can cause startup delays/failures

**Prevention Checklist**:

```yaml
# ✅ CORRECT workflow structure
name: CI Pipeline

on:
  push:
    branches: [main, staging]

# ❌ NEVER: Global timeout
# timeout-minutes: 90

jobs:
  job1:
    runs-on: ubicloud-standard-2 # ✅ Valid runner label
    timeout-minutes: 20 # ✅ Job-level timeout
    # ❌ AVOID: needs: [job-on-different-runner-type]

  job2:
    runs-on: [self-hosted, macos]
    timeout-minutes: 30
    # ✅ OK: Same runner type dependencies
    needs: [other-macos-job]
```

**Testing Strategy**:

1. Create minimal test workflow to verify runner availability
2. Test cross-runner communication if dependencies are required
3. Always include explicit timeouts to prevent infinite hangs
4. Use parallel execution where possible for better performance

### CI Performance Optimization Patterns

**Load Distribution Strategy**:

- **Frequent/Fast Jobs** → Ubicloud (cost-effective, fast startup)
- **Platform-Specific Jobs** → Self-hosted (macOS for WebKit, Windows for .NET)
- **Resource-Intensive Jobs** → Appropriate runner type based on requirements

**Parallel vs Sequential Execution**:

```yaml
# ✅ GOOD: Parallel execution for independent jobs
jobs:
  lint:
    runs-on: ubicloud-standard-2
  test:
    runs-on: ubicloud-standard-2
  build:
    runs-on: [self-hosted, macos]

# ❌ AVOID: Unnecessary sequential dependencies
jobs:
  lint:
    runs-on: ubicloud-standard-2
  test:
    runs-on: ubicloud-standard-2
    needs: [lint]  # Unnecessary if tests don't depend on lint results
```

**Timeout Strategy**:

- **Quick checks**: 5-10 minutes
- **Unit tests**: 15-20 minutes
- **Build**: 20-30 minutes
- **E2E tests**: 30-45 minutes
- **Status/reporting**: 5 minutes

This prevents infinite hangs while allowing sufficient time for legitimate operations.

### CI Workflow Optimization and Resource Management

**Issue**: CI workflows consuming excessive resources, failing due to memory constraints, storage quota limits, and inefficient test execution.

**Root Causes and Solutions**:

#### **Memory Optimization for Resource-Constrained Runners**

**Problem**: Trying to allocate more memory than available on runners (e.g., 12GB on 8GB ubicloud runners).

**Solution**: Optimize memory allocation based on runner capacity:

```yaml
# BAD: Exceeds runner capacity
env:
  NODE_OPTIONS: '--max_old_space_size=12288' # 12GB on 8GB runner

# GOOD: Fits within constraints
env:
  NODE_OPTIONS: '--max_old_space_size=6144 --max-semi-space-size=512 --expose-gc'

# Per-job optimization
- name: Run unit tests
  run: |
    export NODE_OPTIONS="--max_old_space_size=4096 --max-semi-space-size=256"
    npx vitest run --coverage
```

**Memory Guidelines**:

- **8GB runners**: Max 6GB allocation (leave 2GB for OS)
- **Unit tests**: 4GB allocation sufficient
- **E2E tests**: 6GB allocation for browser processes
- **Build processes**: 4-6GB depending on complexity

#### **Storage Quota Management**

**Problem**: GitHub artifact storage quota exceeded (187-219 files per run).

**Solution**: Conditional artifact uploads and retention optimization:

```yaml
# BAD: Always upload artifacts
- name: Upload test results
  uses: actions/upload-artifact@v4
  if: always()
  with:
    retention-days: 7

# GOOD: Only upload on failure
- name: Upload test results
  uses: actions/upload-artifact@v4
  if: failure() # Only when needed for debugging
  with:
    retention-days: 3 # Reduced retention
    compression-level: 6 # Enable compression
```

**Storage Optimization Strategy**:

- Upload artifacts only on failure (`if: failure()`)
- Reduce retention from 7 days to 3 days
- Enable compression (`compression-level: 6`)
- **Result**: ~80% storage reduction

#### **Test Execution Strategy by Branch Type**

**Problem**: Running expensive E2E tests on every branch/PR wastes resources.

**Solution**: Branch-based test execution strategy:

```yaml
# E2E tests only on main/staging branches
e2e-critical:
  name: Critical E2E Tests
  runs-on: ubicloud-standard-2
  if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'

# Unit tests on all branches
test-unit:
  name: Unit Tests
  runs-on: ubicloud-standard-2
  # No branch restriction - always run
```

**Test Strategy**:

- **Feature branches**: Unit tests + linting + quick checks (fast feedback)
- **Main/staging**: Full suite including E2E tests (comprehensive validation)
- **Manual override**: Use `full-test` label for complete testing on PRs

#### **Runner Selection and Load Balancing**

**Problem**: Overloading expensive macOS runners with platform-independent tasks.

**Solution**: Strategic runner distribution:

```yaml
# Platform-independent jobs → Linux (ubicloud-standard-2)
test-unit:
  runs-on: ubicloud-standard-2

quality-gates:
  runs-on: ubicloud-standard-2

# Platform-specific jobs → macOS ([self-hosted, macos])
build:
  runs-on: [self-hosted, macos] # Next.js build consistency

quick-checks:
  runs-on: [self-hosted, macos] # TypeScript/linting
```

**Load Balancing Benefits**:

- **Cost reduction**: ~60% savings using Linux for appropriate jobs
- **Performance**: Linux runners often faster for unit tests
- **Availability**: Better runner availability distribution

#### **E2E Test Optimization**

**Problem**: E2E tests failing due to WebKit issues, consuming excessive memory, taking too long.

**Solution**: Browser and configuration optimization:

```typescript
// playwright.ci.optimized.config.ts
export default defineConfig({
  // Chrome-only for memory efficiency (disable WebKit)
  projects: [
    {
      name: 'Mobile Chrome Critical',
      use: { ...devices['Pixel 5'] },
      testMatch: /.*@critical.*\.spec\.ts$/,
      retries: 3,
      workers: 1, // Single worker to prevent memory spikes
    },
    // WebKit projects commented out for memory optimization
  ],

  // Optimized server configuration
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: true, // Prevent port conflicts
    env: {
      NODE_OPTIONS: '--max_old_space_size=4096', // Reduced for 8GB runners
    },
  },
})
```

**E2E Optimization Results**:

- **Memory usage**: ~60% reduction (Chrome-only vs Chrome+WebKit)
- **Test speed**: ~40% faster (no WebKit complexity)
- **Reliability**: Higher success rate on Linux runners

#### **Test Mock Infrastructure**

**Problem**: Test failures due to missing or incorrect mocks for Next.js navigation.

**Solution**: Comprehensive global mock setup:

```typescript
// src/test-utils/setup.ts
export const mockPush = vi.fn()
export const mockRedirect = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: vi.fn(),
    back: vi.fn(),
    refresh: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
    has: vi.fn(),
    // ... all SearchParams methods
  }),
  usePathname: () => '/test-path',
  redirect: mockRedirect,
  notFound: vi.fn(),
}))

// Reset mocks in beforeEach
beforeEach(() => {
  mockPush.mockClear()
  mockRedirect.mockClear()
  // ... reset all mocks
})
```

**Mock Strategy**:

- **Global mocks**: Set up once in setup.ts for consistency
- **Complete coverage**: Mock all used Next.js navigation exports
- **Proper cleanup**: Reset mocks between tests
- **Type safety**: Maintain TypeScript compatibility

#### **CI Status and Conditional Job Handling**

**Problem**: CI status checks failing when conditional jobs don't run.

**Solution**: Smart conditional status checking:

```yaml
ci-status:
  name: CI Status
  needs: [quick-checks, test-unit, e2e-critical, quality-gates]
  if: always()

  steps:
    - name: Check required jobs
      run: |
        # Check core jobs (always required)
        if [[ "${{ needs.quick-checks.result }}" != "success" ||
              "${{ needs.test-unit.result }}" != "success" ||
              "${{ needs.quality-gates.result }}" != "success" ]]; then
          echo "❌ Core checks failed!"
          exit 1
        fi

        # Check E2E tests only if they ran (main/staging branches)
        if [[ "${{ needs.e2e-critical.result }}" == "failure" ]]; then
          echo "❌ Critical E2E tests failed!"
          exit 1
        elif [[ "${{ needs.e2e-critical.result }}" == "success" ]]; then
          echo "✅ All checks passed including E2E tests!"
        else
          echo "✅ All core checks passed! (E2E tests skipped - not main/staging branch)"
        fi
```

#### **Performance Metrics and Results**

**Before Optimization**:

- Memory: 12GB+ allocation (exceeded 8GB runners)
- Storage: 187-219 files × 4 jobs × 7 days = massive usage
- CI Time: ~120 minutes for all branches
- Cost: High (macOS runners for everything)

**After Optimization**:

- Memory: ~6GB total (fits 8GB runners with OS overhead)
- Storage: Only failure artifacts × 3 days = ~80% reduction
- CI Time: ~45 minutes for feature branches, ~120 minutes for main/staging
- Cost: ~60% reduction with strategic runner distribution

**Key Success Metrics**:

- **Feature branch feedback**: 70% faster (45min vs 120min)
- **Resource utilization**: Fits within infrastructure constraints
- **Storage costs**: 80% reduction in artifact storage
- **Test reliability**: Much higher success rate with proper mocks
- **Maintenance overhead**: Significantly reduced with better error handling

#### **CI Configuration Files Reference**

**Workflow Files**:

- `.github/workflows/ci-optimized.yml`: Main CI workflow with resource optimization
- `playwright.ci.optimized.config.ts`: E2E test configuration for CI environment
- `vitest.config.parallel.mjs`: Unit test configuration with parallel execution

**Key Configuration Patterns**:

```yaml
# .github/workflows/ci-optimized.yml
env:
  NODE_OPTIONS: '--max_old_space_size=6144 --max-semi-space-size=512 --expose-gc'

jobs:
  test-unit:
    runs-on: ubicloud-standard-2 # Linux for cost efficiency

  e2e-critical:
    runs-on: ubicloud-standard-2
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'

  build:
    runs-on: [self-hosted, macos] # macOS for Next.js consistency
```

```typescript
// playwright.ci.optimized.config.ts
export default defineConfig({
  projects: [
    // Chrome-only for memory efficiency
    { name: 'Mobile Chrome Critical', use: devices['Pixel 5'] },
    // WebKit disabled to save ~60% memory
  ],
  webServer: {
    reuseExistingServer: true, // Prevent port conflicts
    env: { NODE_OPTIONS: '--max_old_space_size=4096' },
  },
})
```

```javascript
// vitest.config.parallel.mjs
export default defineConfig({
  test: {
    exclude: ['**/e2e/**', 'tests/e2e/**'], // Separate unit from E2E
    pool: 'threads',
    poolOptions: {
      threads: { maxThreads: optimalWorkers },
    },
  },
})
```

**Test Mock Setup**:

```typescript
// src/test-utils/setup.ts - Global mocks
vi.mock('next/navigation', () => ({
  useRouter: () => ({ push: mockPush, replace: mockReplace }),
  useSearchParams: () => ({ get: vi.fn(), has: vi.fn() }),
  redirect: mockRedirect,
}))

// src/test-utils/workout-mocks.ts - Domain-specific mocks
export const mockWorkoutStore = {
  updateActivity: vi.fn(), // Critical for ExercisePageV2Client
  validateSession: vi.fn(() => true),
  // ... other workout-related mocks
}
```

**Troubleshooting Quick Fixes**:

1. **Memory Issues**: Reduce NODE_OPTIONS allocation
2. **Storage Quota**: Add `if: failure()` to artifact uploads
3. **Test Failures**: Check global mocks in setup.ts
4. **Build Failures**: Verify .next directory creation
5. **Port Conflicts**: Set `reuseExistingServer: true`

## Label Capitalization Pattern

### Mobile App Consistency

**Pattern**: Follow the mobile app's label capitalization style for consistency across platforms.

**Rule**: Use lowercase for the second word in multi-word labels (mobile app pattern).

**Examples**:

```
✅ CORRECT (Mobile app pattern):
- "Weeks streak" (not "Week Streak")
- "Open workout" (not "Open Workout")
- "Start workout" (not "Start Workout")
- "Continue workout" (not "Continue Workout")
- "lbs lifted" or "kg lifted" (dynamic based on user preference, not "Lbs Lifted")

❌ INCORRECT (Web-style capitalization):
- "Week Streak"
- "Open Workout"
- "Start Workout"
- "Continue Workout"
- "Lbs Lifted" or "Kg Lifted"
```

**Implementation for Dynamic Units**:

```typescript
// Get user's mass unit preference
const { getCachedUserInfo } = useAuthStore()
const userInfo = getCachedUserInfo()
const massUnit = userInfo?.MassUnit === 'kg' ? 'kg' : 'lbs'

// Use in label
label={`${massUnit} lifted`} // Results in "lbs lifted" or "kg lifted"
```

**Key Points**:

- This creates a smoother, more modern UI feel
- Maintains consistency between mobile and web platforms
- Only the first letter of the entire label is capitalized
- Dynamic labels should adapt based on user preferences (e.g., mass units)
