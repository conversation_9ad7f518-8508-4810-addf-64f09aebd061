'use client'

import React, { forwardRef, ButtonHTMLAttributes } from 'react'
import { cn } from '@/lib/utils'
import { themeConfig } from '@/config/theme'

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger' | 'gold'
  size?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  loading?: boolean
  goldGradient?: boolean
  children: React.ReactNode
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      loading = false,
      goldGradient = false,
      className,
      onClick,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      // Haptic feedback for mobile
      if (navigator.vibrate && themeConfig.mobile.haptic.enabled) {
        navigator.vibrate(themeConfig.mobile.haptic.duration)
      }

      if (onClick && !loading) {
        onClick(e)
      }
    }

    const sizeClasses = {
      sm: 'h-13 px-4 text-sm', // 52px height (meets touch target requirement)
      md: 'h-14 px-6 text-base', // 56px height (comfortable)
      lg: 'h-16 px-8 text-lg', // 64px height (large)
    }

    const isDisabled = disabled || loading

    const baseClasses = cn(
      // Base button styles
      'inline-flex items-center justify-center',
      'font-medium transition-all duration-200',
      'touch-manipulation select-none',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
      // Size
      sizeClasses[size],
      // Touch target optimization
      'min-w-[52px]', // Minimum touch target
      !isDisabled && 'active:scale-95', // Touch feedback only when enabled
      // Full width
      fullWidth && 'w-full',
      // Loading state
      loading && 'cursor-wait',
      // Gold CTA label styles
      (goldGradient || variant === 'gold') && 'tracking-wider font-semibold'
    )

    // Variant styles using theme configuration
    const variantStyles = {
      primary: cn(
        'bg-brand-primary text-text-inverse',
        !isDisabled && 'hover:bg-brand-primary/90',
        'shadow-theme-md',
        !isDisabled && 'hover:shadow-theme-lg',
        'rounded-theme',
        'focus-visible:ring-brand-primary'
      ),
      secondary: cn(
        'bg-bg-secondary text-brand-primary',
        'border border-brand-primary/20',
        !isDisabled && 'hover:border-brand-primary/40',
        !isDisabled && 'hover:bg-bg-tertiary',
        'rounded-theme',
        'focus-visible:ring-brand-primary'
      ),
      ghost: cn(
        'text-brand-primary',
        !isDisabled && 'hover:bg-brand-primary/10',
        'rounded-theme',
        'focus-visible:ring-brand-primary'
      ),
      danger: cn(
        'bg-red-600 text-white',
        !isDisabled && 'hover:bg-red-700',
        'shadow-theme-md',
        !isDisabled && 'hover:shadow-theme-lg',
        'rounded-theme',
        'focus-visible:ring-red-600'
      ),
      gold: cn(
        'bg-gradient-metallic-gold text-text-inverse',
        !isDisabled && 'shimmer-hover',
        !isDisabled && 'hover:shadow-xl hover:shadow-brand-primary/20',
        'shadow-theme-md',
        !isDisabled && 'hover:shadow-theme-lg',
        'rounded-theme',
        'text-shadow-sm',
        'focus-visible:ring-brand-primary'
      ),
    }

    const variantClasses =
      goldGradient || variant === 'gold'
        ? cn(
            'bg-gradient-metallic-gold text-text-inverse',
            !isDisabled && 'shimmer-hover',
            !isDisabled && 'hover:shadow-xl hover:shadow-brand-primary/20',
            'shadow-theme-md',
            !isDisabled && 'hover:shadow-theme-lg',
            'rounded-theme',
            'text-shadow-sm',
            'focus-visible:ring-brand-primary'
          )
        : variantStyles[variant]

    return (
      <button
        ref={ref}
        type="button"
        className={cn(baseClasses, variantClasses, className)}
        onClick={handleClick}
        disabled={disabled || loading}
        data-variant={variant}
        data-size={size}
        {...props}
      >
        {loading ? (
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            <span>Loading...</span>
          </div>
        ) : (
          children
        )}
      </button>
    )
  }
)

Button.displayName = 'Button'
