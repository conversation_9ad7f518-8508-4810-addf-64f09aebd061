# Offline Mode Implementation Plan

## Overview

This document outlines the implementation of offline mode for the Dr. Muscle app, allowing users to pre-load workouts while online and complete them without internet connectivity.

## Requirements Summary

### Core Features

- Pre-load workout data while online via "Load Offline Workout" menu option
- Complete entire workout offline (view exercises, log sets, use rest timer)
- Auto-sync completed workout when back online
- One offline workout at a time
- Auto-save progress during workout

### UI/UX

- "Load Offline Workout" menu option (online only, shows loaded state)
- Offline mode indicators (toast + status icon)
- Blocked features show "Features are limited while offline" message
- Automatic sync with progress notifications

### Technical

- Hybrid storage: React Query for API data, Zustand for workout progress
- Browser/device network status detection
- Partial sync support with retry logic
- No version tracking or encryption needed

## Detailed Blueprint

### Phase 1: Storage Foundation

1. Set up React Query persistence for API responses
2. Set up Zustand persistence for workout state
3. Create storage utilities for offline data management

### Phase 2: Network Detection

1. Implement network status monitoring
2. Create network status hooks
3. Add offline mode state management

### Phase 3: Offline Data Loading

1. Add "Load Offline Workout" menu option
2. Implement workout pre-loading logic
3. Create offline data validation

### Phase 4: Offline Mode UI

1. Add offline status indicator
2. Implement toast notifications
3. Update menu item states

### Phase 5: Offline Workout Flow

1. Enable offline workout execution
2. Block non-essential features
3. Implement auto-save progress

### Phase 6: Sync Process

1. Detect online status restoration
2. Implement sync queue
3. Add partial sync support
4. Handle sync conflicts

### Phase 7: Polish & Edge Cases

1. Add loading states
2. Handle errors gracefully
3. Test all edge cases

## Iterative Breakdown - Round 1

### Chunk 1: Storage Setup

- Configure React Query persistence
- Configure Zustand persistence
- Test basic data persistence

### Chunk 2: Network Monitoring

- Add network status detection
- Create useNetworkStatus hook
- Test online/offline detection

### Chunk 3: Menu Integration

- Add menu option
- Show/hide based on network status
- Add loaded state indicator

### Chunk 4: Workout Loading

- Fetch workout data
- Cache in React Query
- Show loading progress

### Chunk 5: Offline Indicators

- Add status icon
- Implement toast system
- Test indicator visibility

### Chunk 6: Offline Workout

- Enable offline workout flow
- Block restricted features
- Auto-save progress

### Chunk 7: Sync Implementation

- Detect reconnection
- Queue sync jobs
- Handle sync completion

## Iterative Breakdown - Round 2 (Refined)

### Step 1: React Query Persistence Setup

- Install persistence dependencies
- Create persistence configuration
- Test with simple query

### Step 2: Zustand Persistence Setup

- Add persist middleware
- Configure IndexedDB adapter
- Test state persistence

### Step 3: Network Status Hook

- Create useNetworkStatus hook
- Add online/offline detection
- Test status changes

### Step 4: Offline Mode Store

- Create offline mode Zustand store
- Add workout loaded state
- Test state management

### Step 5: Menu Option Component

- Create LoadOfflineWorkout component
- Add visibility logic
- Show loaded state

### Step 6: Workout Data Fetching

- Create loadOfflineWorkout function
- Integrate with existing API
- Cache response data

### Step 7: Loading Progress UI

- Add loading toast
- Show progress indicator
- Handle completion/errors

### Step 8: Offline Status Icon

- Create OfflineIndicator component
- Add to app header
- Show/hide based on status

### Step 9: Offline Mode Toast

- Create toast notifications
- Show on status change
- Test visibility

### Step 10: Feature Blocking

- Create feature guard HOC
- Block restricted features
- Show error message

### Step 11: Auto-save Logic

- Add set completion persistence
- Save after each action
- Test data recovery

### Step 12: Sync Queue Setup

- Create sync queue store
- Add workout to queue
- Track sync status

### Step 13: Sync Execution

- Implement sync function
- Handle partial sync
- Update UI on completion

### Step 14: Error Handling

- Add retry logic
- Handle sync failures
- Show error states

### Step 15: Integration Testing

- Test full offline flow
- Verify data integrity
- Polish edge cases

## Final Implementation Steps (Right-Sized)

After review, these steps are sized to be:

- Small enough for safe implementation with tests
- Large enough to show meaningful progress
- Each builds on previous work
- No orphaned code

### Implementation Order:

1. **Storage Foundation** - React Query + Zustand persistence setup
2. **Network Detection** - Hook for online/offline status
3. **Offline State Management** - Store for offline mode data
4. **Menu Component** - Load offline workout option
5. **Data Loading** - Fetch and cache workout data
6. **UI Indicators** - Status icon and toasts
7. **Feature Guards** - Block restricted features offline
8. **Auto-save** - Persist workout progress continuously
9. **Sync Queue** - Track workouts pending sync
10. **Sync Process** - Execute sync when online
11. **Error Recovery** - Handle failures gracefully
12. **Integration** - Wire everything together

## TDD Implementation Prompts

### Prompt 1: Storage Foundation Setup

```text
I need to set up the storage foundation for offline mode in a Next.js PWA using React Query and Zustand.

First, let's implement React Query persistence:
1. Install @tanstack/query-sync-storage-persister and any required dependencies
2. Create a persistence configuration that uses IndexedDB
3. Set up the persister in the React Query client configuration
4. Write tests to verify that API responses are persisted and restored

Then, implement Zustand persistence:
1. Add the persist middleware to relevant Zustand stores
2. Configure it to use IndexedDB for storage
3. Test that state persists across app reloads

Follow TDD - write failing tests first, then implement. Ensure all TypeScript types are properly defined with no 'any' types.
```

### Prompt 2: Network Status Detection

```text
I need to implement network status detection for offline mode support.

Create a useNetworkStatus hook that:
1. Uses the browser's navigator.onLine API
2. Sets up event listeners for online/offline events
3. Returns current network status and subscribes to changes
4. Properly cleans up event listeners

Requirements:
- Write tests first (TDD approach)
- Return { isOnline: boolean, isOffline: boolean }
- Handle SSR properly (Next.js)
- Add proper TypeScript types
- Test online/offline transitions
```

### Prompt 3: Offline Mode State Management

```text
I need to create a Zustand store for managing offline mode state.

Create an offline mode store with:
1. State for tracking if offline workout is loaded
2. State for which workout is loaded (workout ID/details)
3. State for sync status (pending, syncing, synced)
4. Actions to update these states

Requirements:
- Use TDD - write tests first
- Include persistence using the setup from step 1
- Proper TypeScript interfaces
- Test all state transitions
- Include selectors for common queries
```

### Prompt 4: Load Offline Workout Menu Component

```text
I need to create a menu component for loading offline workouts.

Create LoadOfflineWorkout component that:
1. Shows only when online (use useNetworkStatus hook)
2. Shows "Offline Workout (Loaded)" when workout is cached
3. Disabled state when offline with "offline" label
4. Integrates with existing menu structure

Requirements:
- Write component tests first
- Test all visual states (online, offline, loaded, loading)
- Use existing UI components and styles
- Proper TypeScript props interface
- Accessibility compliant
```

### Prompt 5: Workout Data Loading Implementation

```text
I need to implement the workout data loading functionality.

Create loadOfflineWorkout function that:
1. Fetches next workout from API
2. Caches all required data (workout, exercises, recommendations)
3. Updates offline store to mark as loaded
4. Shows loading progress

Requirements:
- TDD approach - write tests for loading logic first
- Integrate with existing API hooks/functions
- Use React Query for caching
- Handle loading states and errors
- Show toast notifications for success/failure
- Ensure all required data is cached
```

### Prompt 6: Offline Mode UI Indicators

```text
I need to implement UI indicators for offline mode.

Create:
1. OfflineIndicator component for header (icon + status)
2. Toast notifications for status changes
3. Integration with existing app layout

Requirements:
- Write tests first for all components
- Toast shows "Entering offline mode" when going offline
- Toast shows "Syncing workout..." when reconnecting
- Small, unobtrusive offline icon in header
- Use existing toast system if available
- Proper animations and transitions
```

### Prompt 7: Feature Guards for Offline Mode

```text
I need to implement feature blocking when in offline mode.

Create:
1. useOfflineGuard hook that checks if feature is available offline
2. OfflineGuard wrapper component
3. Integration with navigation and feature access

Requirements:
- TDD - write tests for guard logic first
- Show "Features are limited while offline" message
- Block non-essential features (profile, settings, etc.)
- Allow core workout features
- Consistent error messaging
- Don't break existing navigation flow
```

### Prompt 8: Auto-save Workout Progress

```text
I need to implement auto-save functionality for offline workouts.

Enhance workout state to:
1. Persist after every set completion
2. Persist timer states
3. Recover state on app restart
4. Handle mid-workout recovery

Requirements:
- Write tests for persistence logic first
- Save to IndexedDB after each user action
- Test crash recovery scenarios
- Ensure no data loss
- Integrate with existing workout flow
- Minimal performance impact
```

### Prompt 9: Sync Queue Implementation

```text
I need to implement a sync queue for offline workouts.

Create sync queue that:
1. Tracks workouts pending sync
2. Maintains sync status per workout
3. Handles partial sync tracking
4. Prevents loading new offline workout if one is pending

Requirements:
- TDD approach for queue logic
- Persist queue state
- Track individual set sync status
- Clear queue after successful sync
- Proper error state handling
- Integration with offline store
```

### Prompt 10: Sync Process Implementation

```text
I need to implement the workout sync process.

Create sync functionality that:
1. Detects when app comes online
2. Automatically starts sync process
3. Syncs workout data to backend
4. Handles partial failures
5. Shows progress notifications

Requirements:
- Write tests for sync logic first
- Use existing API endpoints
- Show "Syncing workout..." toast
- Show "Sync complete ✓" on success
- Retry failed items only
- Update UI state throughout process
- Handle all error scenarios
```

### Prompt 11: Error Handling and Recovery

```text
I need to implement comprehensive error handling for offline mode.

Add error handling for:
1. Failed workout loading
2. Storage quota exceeded
3. Sync failures with retry
4. Network timeout scenarios
5. Data corruption recovery

Requirements:
- TDD - write error case tests first
- User-friendly error messages
- Automatic retry with backoff
- Manual retry options where appropriate
- Preserve workout data on errors
- Log errors for debugging
```

### Prompt 12: Integration and Polish

```text
I need to integrate all offline mode components and polish the feature.

Final integration:
1. Wire all components together in the app
2. Ensure smooth state transitions
3. Add any missing UI polish
4. Verify full offline->online flow
5. Performance optimization

Requirements:
- Write integration tests first
- Test complete user journey
- Ensure no orphaned code
- Optimize bundle size impact
- Smooth animations/transitions
- Final accessibility check
- Update any documentation
```

## Testing Strategy

Each implementation step should follow:

1. Write failing unit tests
2. Write failing integration tests
3. Implement minimal code to pass
4. Refactor while keeping tests green
5. Add edge case tests
6. Verify in browser with mobile viewport

## Success Criteria

- User can load workout while online
- Complete workout fully offline
- Progress auto-saves continuously
- Sync happens automatically when online
- Clear UI feedback throughout
- No data loss in any scenario
- Existing functionality unchanged
