import React from 'react'
import { cn } from '@/lib/utils'

interface NavItem {
  id: string
  label: string
  icon: React.ReactNode
}

interface BottomNavProps {
  items: NavItem[]
  activeId: string
  onItemClick?: (id: string) => void
  className?: string
}

export function BottomNav({
  items,
  activeId,
  onItemClick,
  className,
}: BottomNavProps) {
  return (
    <nav
      className={cn(
        'h-20 bg-black border-t-2 border-[#00FF88] flex items-stretch',
        className
      )}
    >
      {items.map((item) => {
        const isActive = item.id === activeId

        return (
          <button
            key={item.id}
            onClick={() => onItemClick?.(item.id)}
            className={cn(
              'flex-1 flex flex-col items-center justify-center',
              'transition-none uppercase font-bold text-xs',
              'min-w-[52px]',
              isActive
                ? 'bg-[#00FF88] text-black'
                : 'text-white hover:bg-gray-900'
            )}
          >
            <div className="text-2xl mb-1">{item.icon}</div>
            <span>{item.label}</span>
          </button>
        )
      })}
    </nav>
  )
}
