import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '../Button'
import { ThemeProvider } from '../../../theme'

describe('Button Component', () => {
  const renderWithTheme = (ui: React.ReactElement, theme?: string) => {
    return render(
      <ThemeProvider>
        {theme && <div data-theme={theme} />}
        {ui}
      </ThemeProvider>
    )
  }

  describe('Basic Functionality', () => {
    it('should render children correctly', () => {
      renderWithTheme(<Button>Click me</Button>)
      expect(screen.getByText('Click me')).toBeInTheDocument()
    })

    it('should handle click events', () => {
      const handleClick = vi.fn()
      renderWithTheme(<Button onClick={handleClick}>Click me</Button>)

      fireEvent.click(screen.getByText('Click me'))
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('should be disabled when disabled prop is true', () => {
      const handleClick = vi.fn()
      renderWithTheme(
        <Button onClick={handleClick} disabled>
          Click me
        </Button>
      )

      const button = screen.getByText('Click me').closest('button')
      expect(button).toBeDisabled()

      fireEvent.click(button!)
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  describe('Size Variants', () => {
    it('should render small size (52px height)', () => {
      renderWithTheme(<Button size="sm">Small</Button>)
      const button = screen.getByText('Small').closest('button')
      expect(button).toHaveClass('h-13') // 52px = 13 * 4px
    })

    it('should render medium size (56px height)', () => {
      renderWithTheme(<Button size="md">Medium</Button>)
      const button = screen.getByText('Medium').closest('button')
      expect(button).toHaveClass('h-14') // 56px = 14 * 4px
    })

    it('should render large size (64px height)', () => {
      renderWithTheme(<Button size="lg">Large</Button>)
      const button = screen.getByText('Large').closest('button')
      expect(button).toHaveClass('h-16') // 64px = 16 * 4px
    })

    it('should default to medium size', () => {
      renderWithTheme(<Button>Default</Button>)
      const button = screen.getByText('Default').closest('button')
      expect(button).toHaveClass('h-14')
    })
  })

  describe('Variants', () => {
    it('should render primary variant', () => {
      renderWithTheme(<Button variant="primary">Primary</Button>)
      const button = screen.getByText('Primary').closest('button')
      expect(button).toHaveAttribute('data-variant', 'primary')
    })

    it('should render secondary variant', () => {
      renderWithTheme(<Button variant="secondary">Secondary</Button>)
      const button = screen.getByText('Secondary').closest('button')
      expect(button).toHaveAttribute('data-variant', 'secondary')
    })

    it('should render ghost variant', () => {
      renderWithTheme(<Button variant="ghost">Ghost</Button>)
      const button = screen.getByText('Ghost').closest('button')
      expect(button).toHaveAttribute('data-variant', 'ghost')
    })

    it('should default to primary variant', () => {
      renderWithTheme(<Button>Default</Button>)
      const button = screen.getByText('Default').closest('button')
      expect(button).toHaveAttribute('data-variant', 'primary')
    })
  })

  describe('Touch Target Requirements', () => {
    it('should meet minimum touch target of 52px', () => {
      renderWithTheme(<Button size="sm">Small</Button>)
      const button = screen.getByText('Small').closest('button')
      // h-13 = 52px which meets the 52px minimum
      expect(button).toHaveClass('h-13')
    })

    it('should have proper padding for touch accuracy', () => {
      renderWithTheme(<Button>Touch Target</Button>)
      const button = screen.getByText('Touch Target').closest('button')
      expect(button).toHaveClass('px-6') // Generous horizontal padding
    })
  })

  describe('Accessibility', () => {
    it('should support aria-label', () => {
      renderWithTheme(<Button aria-label="Save document">Save</Button>)
      const button = screen.getByLabelText('Save document')
      expect(button).toBeInTheDocument()
    })

    it('should have type="button" by default', () => {
      renderWithTheme(<Button>Button</Button>)
      const button = screen.getByText('Button').closest('button')
      expect(button).toHaveAttribute('type', 'button')
    })

    it('should support type="submit"', () => {
      renderWithTheme(<Button type="submit">Submit</Button>)
      const button = screen.getByText('Submit').closest('button')
      expect(button).toHaveAttribute('type', 'submit')
    })
  })

  describe('Theme Integration', () => {
    it('should apply theme-specific classes', () => {
      renderWithTheme(<Button className="custom-class">Themed</Button>)
      const button = screen.getByText('Themed').closest('button')
      expect(button).toHaveClass('custom-class')
    })

    it('should include haptic feedback trigger', () => {
      const mockVibrate = vi.fn()
      global.navigator.vibrate = mockVibrate

      renderWithTheme(<Button>Haptic</Button>)
      const button = screen.getByText('Haptic').closest('button')

      fireEvent.click(button!)
      // Haptic feedback should be triggered on click
      expect(mockVibrate).toHaveBeenCalledWith(10)
    })

    it('should update styles when theme changes', () => {
      const { rerender } = render(
        <ThemeProvider>
          <Button variant="primary">Theme Test</Button>
        </ThemeProvider>
      )

      const button = screen.getByText('Theme Test').closest('button')

      // In subtle-depth theme (default), primary buttons have gold gradient
      expect(button).toHaveClass('bg-gradient-metallic-gold')
      expect(button).toHaveClass('text-text-inverse')

      // Change theme and verify button updates
      document.documentElement.setAttribute('data-theme', 'flat-bold')

      rerender(
        <ThemeProvider>
          <Button variant="primary">Theme Test</Button>
        </ThemeProvider>
      )

      // Button should still have theme-aware classes that update with CSS variables
      expect(button).toHaveClass('text-text-inverse')
    })

    it('should apply gold gradient when goldGradient prop is true', () => {
      renderWithTheme(<Button goldGradient>Gold Button</Button>)
      const button = screen.getByText('Gold Button').closest('button')

      expect(button).toHaveClass('bg-gradient-metallic-gold')
      expect(button).toHaveClass('shimmer-hover')
      expect(button).toHaveClass('text-shadow-sm')
    })
  })
})
