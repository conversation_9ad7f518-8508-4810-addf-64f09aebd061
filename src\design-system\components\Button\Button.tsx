'use client'

import React, { forwardRef, ButtonHTMLAttributes } from 'react'
import { cn } from '../../../lib/utils'
import { useTheme } from '../../theme/context'

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  goldGradient?: boolean
  children: React.ReactNode
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      goldGradient = false,
      className,
      onClick,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      // Haptic feedback for mobile
      if (navigator.vibrate) {
        navigator.vibrate(10)
      }

      if (onClick) {
        onClick(e)
      }
    }

    const sizeClasses = {
      sm: 'h-13 px-4 text-sm', // 52px height
      md: 'h-14 px-6 text-base', // 56px height
      lg: 'h-16 px-8 text-lg', // 64px height
    }

    const baseClasses = cn(
      // Base button styles
      'inline-flex items-center justify-center',
      'font-medium transition-all duration-200',
      'touch-manipulation select-none',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
      // Size
      sizeClasses[size],
      // Touch target optimization
      'min-w-[52px]', // Minimum touch target
      'active:scale-95' // Touch feedback
    )

    // Variant styles using CSS variables
    const variantStyles = {
      primary: cn(
        'bg-brand-primary text-text-inverse',
        'hover:bg-brand-primary/90',
        'shadow-theme-md hover:shadow-theme-lg',
        'rounded-theme'
      ),
      secondary: cn(
        'bg-bg-secondary text-brand-primary',
        'border border-brand-primary/20 hover:border-brand-primary/40',
        'hover:bg-bg-tertiary',
        'rounded-theme'
      ),
      ghost: cn(
        'text-brand-primary',
        'hover:bg-brand-primary/10',
        'rounded-theme'
      ),
    }

    const { theme } = useTheme()
    const isSubtleDepth = theme === 'subtle-depth'

    // Enhanced styles for subtle-depth theme
    const subtleDepthEnhancements = {
      primary: isSubtleDepth
        ? cn(
            'bg-gradient-metallic-gold shimmer-hover',
            'hover:shadow-xl hover:shadow-brand-primary/20',
            'text-shadow-sm'
          )
        : '',
      secondary: isSubtleDepth
        ? cn(
            'border-gradient-to-r from-brand-primary/20 to-brand-secondary/20',
            'hover:bg-gradient-overlay-subtle'
          )
        : '',
      ghost: isSubtleDepth ? cn('hover:text-shadow-gold') : '',
    }

    // Apply gold gradient if explicitly requested, or handle glass/standard variants
    const variantClasses = goldGradient
      ? cn(
          'bg-gradient-metallic-gold text-text-inverse',
          'shimmer-hover',
          'hover:shadow-xl hover:shadow-brand-primary/20',
          'shadow-theme-md hover:shadow-theme-lg',
          'rounded-theme',
          'text-shadow-sm'
        )
      : cn(
          variantStyles[variant as keyof typeof variantStyles] || '',
          subtleDepthEnhancements[
            variant as keyof typeof subtleDepthEnhancements
          ] || ''
        )

    return (
      <button
        ref={ref}
        type="button"
        className={cn(baseClasses, variantClasses, className)}
        onClick={handleClick}
        disabled={disabled}
        data-variant={variant}
        data-size={size}
        {...props}
      >
        {children}
      </button>
    )
  }
)

Button.displayName = 'Button'
