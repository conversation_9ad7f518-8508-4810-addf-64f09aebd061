'use client'

import React from 'react'
import type { SetType } from '@/utils/setTypeUtils'

interface SetTypeBadgeProps {
  setType: SetType
  onClick?: () => void
  variant?: 'default' | 'compact'
}

const setTypeAbbreviations: Record<SetType, string> = {
  Normal: 'Normal',
  'Rest-pause': 'Rest-pause',
  'Back-off': 'Back-off',
  'Drop set': 'Drop set',
  Pyramid: 'Pyramid',
  'Reverse pyramid': 'Rev pyramid',
  'Warm-up': 'Warm-up',
}

export function SetTypeBadge({
  setType,
  onClick,
  variant = 'default',
}: SetTypeBadgeProps) {
  const displayText = setTypeAbbreviations[setType] || setType

  const baseClasses =
    'inline-flex items-center justify-center text-xs font-medium bg-gradient-to-r from-brand-gold-start to-brand-gold-end bg-clip-text text-transparent hover:opacity-90 transition-opacity cursor-pointer'
  const variantClasses =
    variant === 'compact'
      ? 'px-2 py-0.5 min-h-[20px]'
      : 'px-3 py-1 min-h-[28px]'

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses}`}
      aria-label={`${setType} set type - tap for more info`}
    >
      {displayText}
    </button>
  )
}
