'use client'

import { useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { usePWAInstall } from '@/hooks/usePWAInstall'
import { triggerHaptic } from '@/utils/haptic'
import { APP_VERSION } from '@/constants/version'

interface UserMenuProps {
  isOpen: boolean
  onClose: () => void
  user: { email: string; name?: string }
}

export function UserMenu({ isOpen, onClose, user }: UserMenuProps) {
  const { logout, logoutMutation } = useAuth()
  const { canInstall, installPrompt, isInstalling, isInstalled } =
    usePWAInstall()
  const router = useRouter()
  const menuRef = useRef<HTMLDivElement>(null)

  // Close menu on outside click
  useEffect(() => {
    if (!isOpen) return

    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isOpen, onClose])

  // Close menu on escape key
  useEffect(() => {
    if (!isOpen) return

    function handleEscape(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, onClose])

  const handleInstall = async () => {
    const success = await installPrompt()
    if (success) {
      onClose()
    }
  }

  const handleLogout = async () => {
    triggerHaptic('medium')

    // Perform logout (authStore now handles all cache clearing including React Query)
    await logout()
    onClose()
    router.push('/login')
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-40 bg-bg-overlay"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Menu */}
      <div
        ref={menuRef}
        className="fixed top-14 right-4 z-50 w-64 bg-bg-secondary rounded-theme shadow-theme-xl border border-brand-primary/20 overflow-hidden animate-in slide-in-from-top-2 duration-200"
        role="dialog"
        aria-label="User menu"
      >
        {/* User Info */}
        <div className="p-4 border-b border-brand-primary/20">
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-text-primary truncate">
              {user.name || user.email}
            </p>
            <p className="text-xs text-text-tertiary truncate">{user.email}</p>
          </div>
        </div>

        {/* Menu Items */}
        <div className="py-2">
          {/* Install App Option */}
          {canInstall && (
            <button
              onClick={handleInstall}
              disabled={isInstalling}
              className="w-full flex items-center px-4 py-3 text-sm text-text-primary hover:bg-bg-tertiary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg
                className="w-5 h-5 mr-3 text-brand-primary"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z"
                />
              </svg>
              {isInstalling ? 'Installing...' : 'Install App'}
            </button>
          )}

          {/* App Installed Status */}
          {isInstalled && (
            <div className="w-full flex items-center px-4 py-3 text-sm text-brand-accent">
              <svg
                className="w-5 h-5 mr-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              App Installed
            </div>
          )}

          {/* Logout Option */}
          <button
            onClick={handleLogout}
            disabled={logoutMutation.isPending}
            className="w-full flex items-center px-4 py-3 text-sm text-text-primary hover:bg-bg-tertiary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg
              className="w-5 h-5 mr-3 text-text-tertiary"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
              />
            </svg>
            {logoutMutation.isPending ? 'Logging out...' : 'Log out'}
          </button>
        </div>

        {/* Version Footer */}
        <div className="p-4 pt-2 border-t border-brand-primary/10">
          <p className="text-xs text-text-tertiary text-center">
            v{APP_VERSION}
          </p>
        </div>
      </div>
    </>
  )
}
