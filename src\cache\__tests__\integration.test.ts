/**
 * Integration tests for the unified cache system
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  createCacheManager,
  createDefaultCacheManager,
  createSessionCacheManager,
  createMemoryCacheManager,
  getGlobalCacheManager,
  setGlobalCacheManager,
  clearGlobalCacheManager,
  checkCacheHealth,
  clearAllCacheData,
} from '../index'
import { TestDataGenerators } from './test-utils'

// Mock localStorage and sessionStorage
const createMockStorage = () => {
  let store: Record<string, string> = {}
  
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      store = {}
    }),
    get length() {
      return Object.keys(store).length
    },
    key: vi.fn((index: number) => {
      const keys = Object.keys(store)
      return keys[index] || null
    }),
  }
}

const mockLocalStorage = createMockStorage()
const mockSessionStorage = createMockStorage()

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
})

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
  writable: true,
})

describe('Cache System Integration', () => {
  beforeEach(() => {
    mockLocalStorage.clear()
    mockSessionStorage.clear()
    vi.clearAllMocks()
    clearGlobalCacheManager()
  })

  afterEach(() => {
    clearGlobalCacheManager()
  })

  describe('Factory Functions', () => {
    it('should create cache manager with memory adapter', async () => {
      const manager = createCacheManager({
        adapter: 'memory',
        config: { defaultNamespace: 'test' },
      })

      await manager.set('test-key', 'test-value')
      const result = await manager.get('test-key')

      expect(result).toBe('test-value')
      manager.destroy()
    })

    it('should create cache manager with localStorage adapter', async () => {
      const manager = createCacheManager({
        adapter: 'localStorage',
        config: { defaultNamespace: 'test' },
      })

      await manager.set('test-key', 'test-value')
      const result = await manager.get('test-key')

      expect(result).toBe('test-value')
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
      manager.destroy()
    })

    it('should create cache manager with sessionStorage adapter', async () => {
      const manager = createCacheManager({
        adapter: 'sessionStorage',
        config: { defaultNamespace: 'test' },
      })

      await manager.set('test-key', 'test-value')
      const result = await manager.get('test-key')

      expect(result).toBe('test-value')
      expect(mockSessionStorage.setItem).toHaveBeenCalled()
      manager.destroy()
    })

    it('should fallback to memory adapter when localStorage unavailable', () => {
      // Mock localStorage as unavailable
      Object.defineProperty(window, 'localStorage', {
        value: undefined,
        writable: true,
      })

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      const manager = createCacheManager({
        adapter: 'localStorage',
        config: { defaultNamespace: 'test' },
      })

      expect(consoleSpy).toHaveBeenCalledWith(
        'localStorage is not available, falling back to memory adapter'
      )

      manager.destroy()
      consoleSpy.mockRestore()

      // Restore localStorage
      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      })
    })
  })

  describe('Preset Cache Managers', () => {
    it('should create default cache manager', async () => {
      const manager = createDefaultCacheManager()

      await manager.set('test-key', 'test-value')
      const result = await manager.get('test-key')

      expect(result).toBe('test-value')
      manager.destroy()
    })

    it('should create session cache manager', async () => {
      const manager = createSessionCacheManager()

      await manager.set('session-key', 'session-value')
      const result = await manager.get('session-key')

      expect(result).toBe('session-value')
      manager.destroy()
    })

    it('should create memory cache manager', async () => {
      const manager = createMemoryCacheManager()

      await manager.set('memory-key', 'memory-value')
      const result = await manager.get('memory-key')

      expect(result).toBe('memory-value')
      manager.destroy()
    })
  })

  describe('Global Cache Manager', () => {
    it('should provide singleton global cache manager', async () => {
      const manager1 = getGlobalCacheManager()
      const manager2 = getGlobalCacheManager()

      expect(manager1).toBe(manager2)

      await manager1.set('global-key', 'global-value')
      const result = await manager2.get('global-key')

      expect(result).toBe('global-value')
    })

    it('should allow setting custom global cache manager', async () => {
      const customManager = createMemoryCacheManager()
      setGlobalCacheManager(customManager)

      const globalManager = getGlobalCacheManager()
      expect(globalManager).toBe(customManager)

      await globalManager.set('custom-key', 'custom-value')
      const result = await customManager.get('custom-key')

      expect(result).toBe('custom-value')
    })

    it('should clear global cache manager', () => {
      const manager1 = getGlobalCacheManager()
      clearGlobalCacheManager()
      const manager2 = getGlobalCacheManager()

      expect(manager1).not.toBe(manager2)
    })
  })

  describe('Cross-Adapter Compatibility', () => {
    it('should work with different data types across adapters', async () => {
      const testData = TestDataGenerators.createTestData()
      const adapters = ['memory', 'localStorage', 'sessionStorage'] as const

      for (const adapterType of adapters) {
        const manager = createCacheManager({
          adapter: adapterType,
          config: { defaultNamespace: 'test' },
        })

        // Test each data type
        for (const [key, value] of Object.entries(testData)) {
          await manager.set(`${adapterType}-${key}`, value)
          const result = await manager.get(`${adapterType}-${key}`)
          expect(result).toEqual(value)
        }

        manager.destroy()
      }
    })

    it('should handle TTL consistently across adapters', async () => {
      const adapters = ['memory', 'localStorage', 'sessionStorage'] as const
      const shortTTL = 100 // 100ms

      for (const adapterType of adapters) {
        const manager = createCacheManager({
          adapter: adapterType,
          config: { defaultNamespace: 'test' },
        })

        await manager.set('ttl-key', 'ttl-value', { ttl: shortTTL })
        
        // Should be available immediately
        expect(await manager.get('ttl-key')).toBe('ttl-value')
        
        // Wait for expiration
        await new Promise(resolve => setTimeout(resolve, shortTTL + 50))
        
        // Should be expired
        expect(await manager.get('ttl-key')).toBeNull()

        manager.destroy()
      }
    })
  })

  describe('Namespace Isolation', () => {
    it('should isolate data by namespace across adapters', async () => {
      const manager = createDefaultCacheManager()

      await manager.set('shared-key', 'value1', { namespace: 'ns1' })
      await manager.set('shared-key', 'value2', { namespace: 'ns2' })

      expect(await manager.get('shared-key', 'ns1')).toBe('value1')
      expect(await manager.get('shared-key', 'ns2')).toBe('value2')

      manager.destroy()
    })

    it('should clear namespace independently', async () => {
      const manager = createDefaultCacheManager()

      await manager.set('key1', 'value1', { namespace: 'ns1' })
      await manager.set('key2', 'value2', { namespace: 'ns2' })

      await manager.clear('ns1')

      expect(await manager.get('key1', 'ns1')).toBeNull()
      expect(await manager.get('key2', 'ns2')).toBe('value2')

      manager.destroy()
    })
  })

  describe('Health Checks', () => {
    it('should perform health check on cache system', async () => {
      const manager = createDefaultCacheManager()
      const health = await checkCacheHealth(manager)

      expect(health.isHealthy).toBe(true)
      expect(health.errors).toHaveLength(0)
      expect(health.stats).toBeDefined()
      expect(health.adapters.localStorage).toBe(true)
      expect(health.adapters.sessionStorage).toBe(true)

      manager.destroy()
    })

    it('should detect unhealthy cache system', async () => {
      const manager = createDefaultCacheManager()
      
      // Mock a failing operation
      vi.spyOn(manager, 'set').mockRejectedValueOnce(new Error('Test error'))
      
      const health = await checkCacheHealth(manager)

      expect(health.isHealthy).toBe(false)
      expect(health.errors.length).toBeGreaterThan(0)

      manager.destroy()
    })
  })

  describe('Cleanup Operations', () => {
    it('should clear all cache data', async () => {
      // Set up data in different storages
      const memoryManager = createMemoryCacheManager()
      const localManager = createDefaultCacheManager()
      const sessionManager = createSessionCacheManager()

      await memoryManager.set('memory-key', 'memory-value')
      await localManager.set('local-key', 'local-value')
      await sessionManager.set('session-key', 'session-value')

      // Set global manager
      setGlobalCacheManager(memoryManager)

      const results = await clearAllCacheData()

      expect(results.localStorage).toBe(true)
      expect(results.sessionStorage).toBe(true)
      expect(results.memory).toBe(true)

      // Verify data is cleared
      expect(await memoryManager.get('memory-key')).toBeNull()

      memoryManager.destroy()
      localManager.destroy()
      sessionManager.destroy()
    })
  })

  describe('Error Handling', () => {
    it('should handle unknown adapter type', () => {
      expect(() => createCacheManager({
        adapter: 'unknown' as any,
      })).toThrow('Unknown adapter type: unknown')
    })

    it('should handle adapter initialization errors gracefully', () => {
      // Mock localStorage to throw on access
      Object.defineProperty(window, 'localStorage', {
        get() {
          throw new Error('localStorage access denied')
        },
        configurable: true,
      })

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      const manager = createCacheManager({
        adapter: 'localStorage',
      })

      expect(consoleSpy).toHaveBeenCalledWith(
        'localStorage is not available, falling back to memory adapter'
      )

      manager.destroy()
      consoleSpy.mockRestore()

      // Restore localStorage
      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      })
    })
  })

  describe('Performance', () => {
    it('should handle large datasets efficiently', async () => {
      const manager = createMemoryCacheManager()
      const largeDataset = TestDataGenerators.createPerformanceData().medium

      const startTime = Date.now()

      // Set all entries
      for (const [key, value] of largeDataset) {
        await manager.set(key, value)
      }

      // Get all entries
      for (const [key] of largeDataset) {
        await manager.get(key)
      }

      const endTime = Date.now()
      const duration = endTime - startTime

      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(1000) // 1 second

      manager.destroy()
    })
  })
})
