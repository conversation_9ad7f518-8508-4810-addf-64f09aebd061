/**
 * Recommendation-related actions for workout store
 */

import type { WorkoutState } from './types'
import type { RecommendationModel } from '@/types'
import { logger } from '@/utils/logger'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import { RecommendationLoadingCoordinator } from '@/utils/RecommendationLoadingCoordinator'

export const createRecommendationActions = (
  set: (
    partial:
      | Partial<WorkoutState>
      | ((state: WorkoutState) => Partial<WorkoutState>)
  ) => void,
  get: () => WorkoutState
) => ({
  loadExerciseRecommendation: async (exerciseId: number) => {
    const {
      currentWorkout,
      loadingStates,
      setCachedExerciseRecommendation,
      exercises,
    } = get()

    // Check with coordinator if can start loading
    const coordinator = RecommendationLoadingCoordinator.getInstance()
    if (!coordinator.canStartLoading(exerciseId)) {
      return
    }

    // Check if already loading in local state
    if (loadingStates.get(exerciseId)) {
      return
    }

    // Get workout ID
    const workoutId = currentWorkout?.Id
    if (!workoutId) {
      logger.warn('No current workout ID for recommendation request')
      return
    }

    // Find the exercise to get its SetStyle and IsFlexibility
    const exercise = exercises.find((ex) => ex.Id === exerciseId)
    if (!exercise) {
      logger.warn(`Exercise ${exerciseId} not found in current workout`)
      return
    }

    // Get user from auth state
    const username = getCurrentUserEmail()
    if (!username) {
      logger.warn('No username for recommendation request')
      return
    }

    // Set loading state in both coordinator and local state with timeout
    coordinator.startLoading(exerciseId, { timeout: 30000 })
    set((state) => {
      const newLoadingStates = new Map(state.loadingStates)
      newLoadingStates.set(exerciseId, true)

      // Also update prefetch status for UI indicators
      const newPrefetchStatus = { ...state.prefetchStatus }
      newPrefetchStatus[exerciseId] = 'loading'

      return {
        loadingStates: newLoadingStates,
        prefetchStatus: newPrefetchStatus,
      }
    })

    try {
      // Import getExerciseRecommendation dynamically to avoid circular dependencies
      const { getExerciseRecommendation } = await import(
        '@/services/api/workout'
      )

      const requestBody = {
        Username: username,
        ExerciseId: exerciseId,
        WorkoutId: workoutId,
        SetStyle: exercise.SetStyle,
        IsFlexibility: exercise.IsFlexibility,
        // Using known good defaults from successful tests
        IsQuickMode: false,
        LightSessionDays: 0,
        SwapedExId: undefined,
        IsStrengthPhashe: false, // Note: API has typo
        IsFreePlan: false,
        IsFirstWorkoutOfStrengthPhase: false,
        VersionNo: 1,
      }

      const recommendation = await getExerciseRecommendation(requestBody)

      if (recommendation) {
        // Store only in persisted cache - single source of truth
        setCachedExerciseRecommendation(exerciseId, recommendation)
        // Mark as successfully loaded in coordinator
        coordinator.completeLoading(exerciseId)

        // Update prefetch status to success
        set((state) => {
          const newPrefetchStatus = { ...state.prefetchStatus }
          newPrefetchStatus[exerciseId] = 'success'

          const newPrefetchedIds = [...state.prefetchedExerciseIds]
          if (!newPrefetchedIds.includes(exerciseId)) {
            newPrefetchedIds.push(exerciseId)
          }

          return {
            prefetchStatus: newPrefetchStatus,
            prefetchedExerciseIds: newPrefetchedIds,
          }
        })
      }
    } catch (error) {
      // Mark as failed in coordinator
      coordinator.failLoading(exerciseId)
      // Store error and update prefetch status
      set((state) => {
        const newErrors = new Map(state.errors)
        newErrors.set(
          exerciseId,
          error instanceof Error
            ? error
            : new Error('Failed to load recommendation')
        )

        // Update prefetch status to error
        const newPrefetchStatus = { ...state.prefetchStatus }
        newPrefetchStatus[exerciseId] = 'error'

        return {
          errors: newErrors,
          prefetchStatus: newPrefetchStatus,
        }
      })
      logger.error(
        `Failed to load recommendation for exercise ${exerciseId}:`,
        error
      )
    } finally {
      // Clear loading state
      set((state) => {
        const newLoadingStates = new Map(state.loadingStates)
        newLoadingStates.delete(exerciseId)
        return { loadingStates: newLoadingStates }
      })
    }
  },

  loadAllExerciseRecommendations: async () => {
    const { exercises, currentWorkout, loadExerciseRecommendation } = get()

    if (!exercises || exercises.length === 0) {
      logger.warn('No exercises to load recommendations for')
      return
    }

    if (!currentWorkout?.Id) {
      logger.warn('No current workout ID for recommendations request')
      return
    }

    logger.info(
      `Loading recommendations for ${exercises.length} exercises in parallel`
    )

    // Load all recommendations in parallel
    const recommendationPromises = exercises.map((exercise) =>
      loadExerciseRecommendation(exercise.Id)
    )

    try {
      await Promise.all(recommendationPromises)
      logger.info('All exercise recommendations loaded successfully')
    } catch (error) {
      logger.error('Failed to load some exercise recommendations:', error)
      // Individual errors are already handled in loadExerciseRecommendation
    }
  },

  getCacheKey: (
    userId: string,
    exerciseId: number,
    workoutId: number
  ): string => {
    return `${userId}-${exerciseId}-${workoutId}`
  },

  // Selectors
  getExerciseRecommendation: (
    exerciseId: number
  ): RecommendationModel | undefined => {
    const { getCachedExerciseRecommendation } = get()

    // Use the same persisted cache that loadRecommendation checks
    // This ensures consistency - single source of truth
    const cached = getCachedExerciseRecommendation(exerciseId)

    // Convert null to undefined to match the return type
    return cached === null ? undefined : cached
  },

  isExerciseLoading: (exerciseId: number): boolean => {
    return get().loadingStates.get(exerciseId) || false
  },

  hasActiveRecommendationLoads: (): boolean => {
    try {
      const coordinator = RecommendationLoadingCoordinator.getInstance()
      const loadingIds = coordinator.getLoadingExerciseIds()
      return loadingIds.length > 0
    } catch (error) {
      // If coordinator fails, assume loads are active to be safe
      return true
    }
  },
})
