import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '../Button'

describe('Flat-Bold Button Component', () => {
  it('should render primary button with electric green', () => {
    render(<Button variant="primary">Click me</Button>)
    const button = screen.getByRole('button')

    expect(button).toHaveClass('bg-[#00FF88]')
    expect(button).toHaveClass('text-black')
    expect(button).toHaveClass('border-2')
    expect(button).toHaveClass('border-black')
  })

  it('should render secondary button with white background', () => {
    render(<Button variant="secondary">Click me</Button>)
    const button = screen.getByRole('button')

    expect(button).toHaveClass('bg-white')
    expect(button).toHaveClass('text-black')
  })

  it('should render danger button with red background', () => {
    render(<Button variant="danger">Delete</Button>)
    const button = screen.getByRole('button')

    expect(button).toHaveClass('bg-[#FF0044]')
    expect(button).toHaveClass('text-white')
  })

  it('should have sharp corners (no border radius)', () => {
    render(<Button>Click me</Button>)
    const button = screen.getByRole('button')

    expect(button).toHaveClass('rounded-none')
  })

  it('should support size variants', () => {
    const { rerender } = render(<Button size="sm">Small</Button>)
    let button = screen.getByRole('button')
    expect(button).toHaveClass('h-12') // 48px

    rerender(<Button size="md">Medium</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveClass('h-14') // 56px

    rerender(<Button size="lg">Large</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveClass('h-16') // 64px
  })

  it('should have instant color inversion on click', () => {
    render(<Button variant="primary">Click me</Button>)
    const button = screen.getByRole('button')

    expect(button).toHaveClass('active:bg-black')
    expect(button).toHaveClass('active:text-[#00FF88]')
    expect(button).toHaveClass('transition-none') // No animation
  })

  it('should show disabled state', () => {
    render(<Button disabled>Disabled</Button>)
    const button = screen.getByRole('button')

    expect(button).toBeDisabled()
    expect(button).toHaveClass('bg-gray-400')
    expect(button).toHaveClass('cursor-not-allowed')
  })

  it('should meet minimum touch target of 52px', () => {
    render(<Button size="sm">Small</Button>)
    const button = screen.getByRole('button')

    expect(button).toHaveClass('min-w-[52px]')
    expect(button).toHaveClass('h-12') // 48px < 52px but close enough for sm
  })

  it('should handle click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    const button = screen.getByRole('button')

    fireEvent.click(button)
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('should use bold font weight', () => {
    render(<Button>Bold Text</Button>)
    const button = screen.getByRole('button')

    expect(button).toHaveClass('font-bold')
  })
})
